import { GameEngine } from '../../services/gameEngine.js';
import { GameObject, Player, Obstacle } from '../../services/GameObject.js';

// Crossy Road specific classes
class Character extends Player {
  constructor(x, y) {
    super(x, y, 30, 30);
    this.gridX = Math.floor(x / 40);
    this.gridY = Math.floor(y / 40);
    this.targetX = x;
    this.targetY = y;
    this.moving = false;
    this.moveSpeed = 200;
    this.color = '#FFD700';
  }

  update(deltaTime) {
    super.update(deltaTime);
    
    if (this.moving) {
      const dx = this.targetX - this.x;
      const dy = this.targetY - this.y;
      const distance = Math.sqrt(dx * dx + dy * dy);
      
      if (distance < 2) {
        this.x = this.targetX;
        this.y = this.targetY;
        this.moving = false;
      } else {
        this.x += (dx / distance) * this.moveSpeed * deltaTime;
        this.y += (dy / distance) * this.moveSpeed * deltaTime;
      }
    }
  }

  render(ctx) {
    ctx.fillStyle = this.color;
    ctx.fillRect(this.x + 2, this.y + 2, this.width - 4, this.height - 4);
    
    // Draw character details
    ctx.fillStyle = '#FFE4B5'; // Head
    ctx.fillRect(this.x + 8, this.y + 5, 14, 12);
    
    ctx.fillStyle = '#000'; // Eyes
    ctx.fillRect(this.x + 10, this.y + 8, 2, 2);
    ctx.fillRect(this.x + 18, this.y + 8, 2, 2);
    
    ctx.fillStyle = '#FF6B6B'; // Body
    ctx.fillRect(this.x + 6, this.y + 17, 18, 10);
  }

  moveTo(gridX, gridY) {
    if (this.moving) return false;
    
    this.gridX = gridX;
    this.gridY = gridY;
    this.targetX = gridX * 40;
    this.targetY = gridY * 40;
    this.moving = true;
    return true;
  }

  canMoveTo(gridX, gridY, gameWidth, gameHeight) {
    return gridX >= 0 && gridX < gameWidth / 40 && 
           gridY >= 0 && gridY < gameHeight / 40;
  }
}

class Car extends Obstacle {
  constructor(x, y, direction, speed, color = '#FF0000') {
    super(x, y, 60, 30);
    this.type = 'car';
    this.direction = direction; // 1 for right, -1 for left
    this.speed = speed;
    this.color = color;
  }

  update(deltaTime) {
    super.update(deltaTime);
    this.x += this.direction * this.speed * deltaTime;
  }

  render(ctx) {
    ctx.fillStyle = this.color;
    ctx.fillRect(this.x, this.y + 5, this.width, this.height - 10);
    
    // Car details
    ctx.fillStyle = '#87CEEB'; // Windows
    ctx.fillRect(this.x + 10, this.y + 8, this.width - 20, this.height - 16);
    
    ctx.fillStyle = '#2F2F2F'; // Wheels
    ctx.fillRect(this.x + 5, this.y + this.height - 8, 8, 6);
    ctx.fillRect(this.x + this.width - 13, this.y + this.height - 8, 8, 6);
    
    // Headlights/Taillights
    ctx.fillStyle = this.direction > 0 ? '#FFFF00' : '#FF0000';
    ctx.fillRect(this.direction > 0 ? this.x + this.width - 5 : this.x, this.y + 10, 5, 8);
  }

  isOffScreen(canvasWidth) {
    return (this.direction > 0 && this.x > canvasWidth + 100) ||
           (this.direction < 0 && this.x + this.width < -100);
  }
}

class Log extends GameObject {
  constructor(x, y, width, direction, speed) {
    super(x, y, width, 30);
    this.type = 'log';
    this.direction = direction;
    this.speed = speed;
    this.color = '#8B4513';
    this.passengers = [];
  }

  update(deltaTime) {
    super.update(deltaTime);
    this.x += this.direction * this.speed * deltaTime;
    
    // Move passengers with the log
    this.passengers.forEach(passenger => {
      passenger.x += this.direction * this.speed * deltaTime;
    });
  }

  render(ctx) {
    ctx.fillStyle = this.color;
    ctx.fillRect(this.x, this.y + 5, this.width, this.height - 10);
    
    // Log texture
    ctx.strokeStyle = '#654321';
    ctx.lineWidth = 2;
    for (let i = 0; i < this.width; i += 20) {
      ctx.beginPath();
      ctx.moveTo(this.x + i, this.y + 5);
      ctx.lineTo(this.x + i, this.y + this.height - 5);
      ctx.stroke();
    }
  }

  addPassenger(character) {
    if (!this.passengers.includes(character)) {
      this.passengers.push(character);
    }
  }

  removePassenger(character) {
    const index = this.passengers.indexOf(character);
    if (index > -1) {
      this.passengers.splice(index, 1);
    }
  }

  isOffScreen(canvasWidth) {
    return (this.direction > 0 && this.x > canvasWidth + 100) ||
           (this.direction < 0 && this.x + this.width < -100);
  }
}

class Lane {
  constructor(y, type, direction = 1, speed = 100) {
    this.y = y;
    this.type = type; // 'safe', 'road', 'water'
    this.direction = direction;
    this.speed = speed;
    this.objects = [];
    this.lastSpawn = 0;
    this.spawnRate = type === 'road' ? 2000 : 3000; // milliseconds
  }

  update(deltaTime, canvasWidth) {
    // Update objects
    this.objects.forEach(obj => obj.update(deltaTime));
    
    // Remove off-screen objects
    this.objects = this.objects.filter(obj => !obj.isOffScreen(canvasWidth));
    
    // Spawn new objects
    this.lastSpawn += deltaTime * 1000;
    if (this.lastSpawn >= this.spawnRate) {
      this.spawnObject(canvasWidth);
      this.lastSpawn = 0;
    }
  }

  spawnObject(canvasWidth) {
    const x = this.direction > 0 ? -100 : canvasWidth + 100;
    
    if (this.type === 'road') {
      const colors = ['#FF0000', '#0000FF', '#00FF00', '#FFFF00', '#FF00FF'];
      const color = colors[Math.floor(Math.random() * colors.length)];
      const car = new Car(x, this.y, this.direction, this.speed, color);
      this.objects.push(car);
    } else if (this.type === 'water') {
      const width = 80 + Math.random() * 120;
      const log = new Log(x, this.y, width, this.direction, this.speed);
      this.objects.push(log);
    }
  }

  render(ctx, canvasWidth) {
    // Draw lane background
    if (this.type === 'road') {
      ctx.fillStyle = '#2F2F2F';
      ctx.fillRect(0, this.y, canvasWidth, 40);
      
      // Road markings
      ctx.fillStyle = '#FFFF00';
      for (let x = 0; x < canvasWidth; x += 60) {
        ctx.fillRect(x, this.y + 18, 30, 4);
      }
    } else if (this.type === 'water') {
      ctx.fillStyle = '#4169E1';
      ctx.fillRect(0, this.y, canvasWidth, 40);
      
      // Water effect
      ctx.fillStyle = 'rgba(135, 206, 235, 0.5)';
      for (let x = 0; x < canvasWidth; x += 40) {
        ctx.fillRect(x + Math.sin(Date.now() * 0.005 + x * 0.1) * 5, this.y + 10, 20, 20);
      }
    } else {
      ctx.fillStyle = '#90EE90';
      ctx.fillRect(0, this.y, canvasWidth, 40);
    }
    
    // Draw objects
    this.objects.forEach(obj => obj.render(ctx));
  }
}

export class CrossyRoadGame extends GameEngine {
  constructor(canvas, config = {}) {
    const defaultConfig = {
      width: 800,
      height: 600,
      background: { color: '#87CEEB' },
      difficulty: 'medium',
      laneHeight: 40,
      ...config
    };
    
    super(canvas, defaultConfig);
    
    this.character = null;
    this.lanes = [];
    this.cameraY = 0;
    this.gameStarted = false;
    this.furthestY = 0;
    this.onLog = null;
    
    this.setupGame();
  }

  setupGame() {
    // Create character
    this.character = new Character(this.canvas.width / 2, this.canvas.height - 80);
    this.addGameObject(this.character);
    
    // Create lanes
    this.generateLanes();
    
    this.setupControls();
  }

  generateLanes() {
    const laneTypes = ['safe', 'road', 'water', 'safe'];
    const directions = [1, -1];
    
    for (let i = 0; i < 20; i++) {
      const y = this.canvas.height - (i + 1) * this.config.laneHeight;
      let type = 'safe';
      
      if (i > 0 && i % 4 !== 0) {
        type = Math.random() < 0.6 ? 'road' : 'water';
      }
      
      const direction = directions[Math.floor(Math.random() * directions.length)];
      const speed = 50 + Math.random() * 100;
      
      const lane = new Lane(y, type, direction, speed);
      this.lanes.push(lane);
    }
  }

  setupControls() {
    const handleMove = (direction) => {
      if (!this.gameStarted) {
        this.gameStarted = true;
        this.start();
        return;
      }
      
      if (this.gameState !== 'playing' || this.character.moving) return;
      
      let newGridX = this.character.gridX;
      let newGridY = this.character.gridY;
      
      switch (direction) {
        case 'up':
          newGridY--;
          break;
        case 'down':
          newGridY++;
          break;
        case 'left':
          newGridX--;
          break;
        case 'right':
          newGridX++;
          break;
      }
      
      if (this.character.canMoveTo(newGridX, newGridY, this.canvas.width, this.canvas.height * 2)) {
        this.character.moveTo(newGridX, newGridY);
        
        if (direction === 'up') {
          this.furthestY = Math.min(this.furthestY, this.character.y);
          this.updateScore(10);
        }
      }
    };

    window.addEventListener('keydown', (e) => {
      switch (e.code) {
        case 'ArrowUp':
        case 'KeyW':
          e.preventDefault();
          handleMove('up');
          break;
        case 'ArrowDown':
        case 'KeyS':
          e.preventDefault();
          handleMove('down');
          break;
        case 'ArrowLeft':
        case 'KeyA':
          e.preventDefault();
          handleMove('left');
          break;
        case 'ArrowRight':
        case 'KeyD':
          e.preventDefault();
          handleMove('right');
          break;
      }
    });

    // Touch controls for mobile
    let touchStartX, touchStartY;
    
    this.canvas.addEventListener('touchstart', (e) => {
      e.preventDefault();
      const touch = e.touches[0];
      const rect = this.canvas.getBoundingClientRect();
      touchStartX = touch.clientX - rect.left;
      touchStartY = touch.clientY - rect.top;
    });

    this.canvas.addEventListener('touchend', (e) => {
      e.preventDefault();
      if (!touchStartX || !touchStartY) return;
      
      const touch = e.changedTouches[0];
      const rect = this.canvas.getBoundingClientRect();
      const touchEndX = touch.clientX - rect.left;
      const touchEndY = touch.clientY - rect.top;
      
      const dx = touchEndX - touchStartX;
      const dy = touchEndY - touchStartY;
      
      if (Math.abs(dx) > Math.abs(dy)) {
        handleMove(dx > 0 ? 'right' : 'left');
      } else {
        handleMove(dy > 0 ? 'down' : 'up');
      }
    });
  }

  update(deltaTime) {
    if (!this.gameStarted) return;
    
    super.update(deltaTime);
    
    // Update camera to follow character
    this.cameraY = this.character.y - this.canvas.height / 2;
    
    // Update lanes
    this.lanes.forEach(lane => lane.update(deltaTime, this.canvas.width));
    
    // Check collisions and interactions
    this.checkInteractions();
    
    // Check if character is off screen
    if (this.character.x < -50 || this.character.x > this.canvas.width + 50) {
      this.gameOver();
    }
  }

  checkInteractions() {
    const charLaneIndex = Math.floor((this.canvas.height - this.character.y) / this.config.laneHeight) - 1;
    if (charLaneIndex < 0 || charLaneIndex >= this.lanes.length) return;
    
    const currentLane = this.lanes[charLaneIndex];
    this.onLog = null;
    
    // Check interactions with lane objects
    currentLane.objects.forEach(obj => {
      if (this.physics.checkCollision(this.character, obj)) {
        if (obj.type === 'car') {
          this.gameOver();
        } else if (obj.type === 'log') {
          this.onLog = obj;
          obj.addPassenger(this.character);
        }
      }
    });
    
    // Check if character is in water without a log
    if (currentLane.type === 'water' && !this.onLog) {
      this.gameOver();
    }
  }

  render() {
    // Save context for camera transform
    this.ctx.save();
    this.ctx.translate(0, -this.cameraY);
    
    // Render lanes
    this.lanes.forEach(lane => lane.render(this.ctx, this.canvas.width));
    
    // Render game objects
    this.gameObjects.forEach(obj => {
      if (obj.render) obj.render(this.ctx);
    });
    
    // Restore context
    this.ctx.restore();
    
    // Render UI (not affected by camera)
    this.renderUI();
  }

  renderUI() {
    // Score and distance
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '20px Arial';
    this.ctx.fillText(`Score: ${this.score}`, 20, 30);
    this.ctx.fillText(`Distance: ${Math.floor((this.canvas.height - this.furthestY) / 40)}`, 20, 60);
    
    if (!this.gameStarted) {
      // Start screen
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Crossy Road', this.canvas.width / 2, this.canvas.height / 2 - 50);
      
      this.ctx.font = '24px Arial';
      this.ctx.fillText('Arrow Keys or Swipe to Move', this.canvas.width / 2, this.canvas.height / 2 + 20);
      this.ctx.textAlign = 'left';
    }
    
    if (this.gameState === 'gameOver') {
      // Game over screen
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Game Over', this.canvas.width / 2, this.canvas.height / 2 - 50);
      
      this.ctx.font = '24px Arial';
      this.ctx.fillText(`Final Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2 + 20);
      this.ctx.textAlign = 'left';
    }
  }

  updateParameters(newParams) {
    Object.assign(this.config, newParams);
  }

  exportGameData() {
    return {
      type: 'crossyroad',
      config: this.config,
      assets: {
        character: this.character.sprite || null,
        cars: null,
        logs: null,
        background: null
      }
    };
  }
}
