import { useState, useEffect } from 'react';
import { Settings, MessageSquare, Sliders, RotateCcw, Play } from 'lucide-react';
import parameterTuner from '../../services/parameterTuner';

const difficultyPresets = [
  { id: 'easy', name: 'Easy', description: 'Relaxed gameplay for beginners', color: '#48bb78' },
  { id: 'medium', name: 'Medium', description: 'Balanced gameplay for average players', color: '#ed8936' },
  { id: 'hard', name: 'Hard', description: 'Challenging gameplay for experienced players', color: '#e53e3e' },
  { id: 'extreme', name: 'Extreme', description: 'Very difficult gameplay for experts', color: '#9f7aea' }
];

const commonRequests = [
  "Make the game easier",
  "Make it more challenging",
  "Speed up the gameplay",
  "Add more obstacles",
  "Make gaps bigger",
  "Increase the time limit",
  "Make it more forgiving",
  "Add more variety"
];

function ParameterTuning({ gameData, onComplete, loading }) {
  const [currentParameters, setCurrentParameters] = useState(null);
  const [selectedDifficulty, setSelectedDifficulty] = useState('medium');
  const [naturalLanguageInput, setNaturalLanguageInput] = useState('');
  const [tuningHistory, setTuningHistory] = useState([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [previewMode, setPreviewMode] = useState(false);

  useEffect(() => {
    if (gameData.template) {
      // Load default parameters for the selected game template
      const defaultParams = parameterTuner.getGameParameters(gameData.template.id);
      setCurrentParameters(defaultParams);
    }
  }, [gameData.template]);

  const handleDifficultyChange = async (difficulty) => {
    if (!gameData.template) return;

    setSelectedDifficulty(difficulty);
    setIsProcessing(true);

    try {
      const adjustedParams = parameterTuner.applyDifficultyPreset(gameData.template.id, difficulty);
      setCurrentParameters(adjustedParams);

      addToHistory(`Applied ${difficulty} difficulty preset`, adjustedParams);
    } catch (error) {
      console.error('Error applying difficulty preset:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleNaturalLanguageTuning = async () => {
    if (!naturalLanguageInput.trim() || !gameData.template || !currentParameters) return;

    setIsProcessing(true);

    try {
      const result = await parameterTuner.tuneParameters(
        gameData.template.id,
        naturalLanguageInput,
        currentParameters
      );

      if (result.success) {
        setCurrentParameters(result.parameters);
        addToHistory(naturalLanguageInput, result.parameters, result.explanation);
        setNaturalLanguageInput('');
      } else {
        console.error('Parameter tuning failed:', result.error);
      }
    } catch (error) {
      console.error('Error tuning parameters:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleQuickRequest = (request) => {
    setNaturalLanguageInput(request);
  };

  const addToHistory = (request, parameters, explanation = null) => {
    const historyEntry = {
      id: Date.now(),
      request,
      parameters: { ...parameters },
      explanation,
      timestamp: new Date().toLocaleTimeString()
    };

    setTuningHistory(prev => [historyEntry, ...prev.slice(0, 4)]); // Keep last 5 entries
  };

  const resetToDefaults = () => {
    if (gameData.template) {
      const defaultParams = parameterTuner.getGameParameters(gameData.template.id);
      setCurrentParameters(defaultParams);
      setSelectedDifficulty('medium');
      setTuningHistory([]);
    }
  };

  const handleContinue = () => {
    onComplete({
      parameters: currentParameters,
      difficulty: selectedDifficulty,
      tuningHistory: tuningHistory
    });
  };

  const renderParameterValue = (key, value) => {
    if (typeof value === 'object') {
      return JSON.stringify(value);
    }
    if (typeof value === 'number') {
      return value.toLocaleString();
    }
    return String(value);
  };

  const getParameterDescription = (key) => {
    const descriptions = {
      difficulty: 'Overall game difficulty level',
      pipeGap: 'Space between pipes (pixels)',
      pipeSpeed: 'Speed of moving pipes (pixels/second)',
      pipeSpawnRate: 'Time between pipe spawns (milliseconds)',
      gravity: 'Gravity force affecting player (pixels/second²)',
      jumpForce: 'Upward force when jumping (pixels/second)',
      scrollSpeed: 'Background scroll speed (pixels/second)',
      platformSpawnRate: 'Platform generation frequency (milliseconds)',
      coinSpawnRate: 'Coin generation frequency (milliseconds)',
      moleSpawnRate: 'Mole appearance frequency (milliseconds)',
      gameTime: 'Total game duration (milliseconds)',
      maxActiveMoles: 'Maximum moles visible at once',
      gridSize: 'Game grid dimensions',
      gemTypes: 'Number of different gem types',
      scoreMultiplier: 'Points multiplier for scoring',
      targetScore: 'Score needed to win',
      laneSpeed: 'Speed of moving objects in lanes',
      carSpawnRate: 'Car generation frequency (milliseconds)',
      characterSpeed: 'Player movement speed (pixels/second)'
    };

    return descriptions[key] || 'Game parameter';
  };

  if (!currentParameters) {
    return (
      <div className="parameter-tuning loading">
        <p>Loading game parameters...</p>
      </div>
    );
  }

  return (
    <div className="parameter-tuning">
      <div className="step-header">
        <h2>Fine-Tune Your Game</h2>
        <p>Adjust gameplay parameters using AI or manual controls to perfect your {gameData.template?.name} game.</p>
      </div>

      <div className="tuning-sections">
        {/* Difficulty Presets */}
        <div className="tuning-section">
          <div className="section-header">
            <Sliders className="section-icon" />
            <h3>Difficulty Presets</h3>
          </div>

          <div className="difficulty-grid">
            {difficultyPresets.map((preset) => (
              <div
                key={preset.id}
                className={`difficulty-card ${selectedDifficulty === preset.id ? 'selected' : ''}`}
                onClick={() => handleDifficultyChange(preset.id)}
                style={{ '--accent-color': preset.color }}
              >
                <h4>{preset.name}</h4>
                <p>{preset.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Natural Language Tuning */}
        <div className="tuning-section">
          <div className="section-header">
            <MessageSquare className="section-icon" />
            <h3>AI Parameter Tuning</h3>
          </div>

          <div className="natural-language-input">
            <textarea
              placeholder="Describe how you want to change the game (e.g., 'make it faster', 'add more obstacles', 'make jumps easier')"
              value={naturalLanguageInput}
              onChange={(e) => {
                console.log('Parameter input change:', e.target.value);
                setNaturalLanguageInput(e.target.value);
              }}
              rows={3}
              style={{ minHeight: '80px' }}
            />
            <small style={{ color: '#666', fontSize: '0.8rem' }}>
              Current value: "{naturalLanguageInput || 'empty'}"
            </small>

            <button
              className="tune-button"
              onClick={handleNaturalLanguageTuning}
              disabled={!naturalLanguageInput.trim() || isProcessing}
            >
              {isProcessing ? 'Processing...' : 'Apply Changes'}
            </button>
          </div>

          <div className="quick-requests">
            <p>Quick suggestions:</p>
            <div className="request-buttons">
              {commonRequests.map((request, index) => (
                <button
                  key={index}
                  className="request-button"
                  onClick={() => handleQuickRequest(request)}
                >
                  {request}
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Current Parameters */}
        <div className="tuning-section">
          <div className="section-header">
            <Settings className="section-icon" />
            <h3>Current Parameters</h3>
            <button className="reset-button" onClick={resetToDefaults}>
              <RotateCcw size={16} />
              Reset to Defaults
            </button>
          </div>

          <div className="parameters-grid">
            {Object.entries(currentParameters).map(([key, value]) => (
              <div key={key} className="parameter-item">
                <div className="parameter-info">
                  <span className="parameter-name">{key}</span>
                  <span className="parameter-description">{getParameterDescription(key)}</span>
                </div>
                <div className="parameter-value">
                  {renderParameterValue(key, value)}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Tuning History */}
        {tuningHistory.length > 0 && (
          <div className="tuning-section">
            <div className="section-header">
              <h3>Recent Changes</h3>
            </div>

            <div className="history-list">
              {tuningHistory.map((entry) => (
                <div key={entry.id} className="history-item">
                  <div className="history-header">
                    <span className="history-request">"{entry.request}"</span>
                    <span className="history-time">{entry.timestamp}</span>
                  </div>
                  {entry.explanation && (
                    <div className="history-explanation">
                      {entry.explanation}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      <div className="step-actions">
        <button
          className="continue-button"
          onClick={handleContinue}
          disabled={loading || isProcessing}
        >
          Continue to Export
        </button>
      </div>
    </div>
  );
}

export default ParameterTuning;
