import { GameEngine } from '../../services/gameEngine.js';
import { GameObject, Player, Obstacle, Collectible } from '../../services/GameObject.js';

// Speed Runner specific classes
class Runner extends Player {
  constructor(x, y) {
    super(x, y, 32, 48);
    this.jumpForce = -500;
    this.speed = 300;
    this.physics.gravity = true;
    this.physics.friction = 0.8;
    this.color = '#00BFFF';
    this.onGround = false;
    this.doubleJumpAvailable = true;
  }

  update(deltaTime) {
    super.update(deltaTime);
    
    // Check if on ground (simplified)
    this.onGround = this.y >= 400; // Ground level
    if (this.onGround) {
      this.doubleJumpAvailable = true;
      this.velocity.y = 0;
      this.y = 400;
    }
  }

  render(ctx) {
    ctx.fillStyle = this.color;
    ctx.fillRect(this.x, this.y, this.width, this.height);
    
    // Draw simple runner
    ctx.fillStyle = '#FFE4B5'; // Skin color
    ctx.fillRect(this.x + 8, this.y + 5, 16, 16); // Head
    
    ctx.fillStyle = '#FF0000'; // Shirt
    ctx.fillRect(this.x + 6, this.y + 21, 20, 15); // Body
    
    ctx.fillStyle = '#0000FF'; // Pants
    ctx.fillRect(this.x + 8, this.y + 36, 16, 12); // Legs
  }

  jump() {
    if (this.onGround) {
      this.velocity.y = this.jumpForce;
      this.onGround = false;
    } else if (this.doubleJumpAvailable) {
      this.velocity.y = this.jumpForce * 0.8;
      this.doubleJumpAvailable = false;
    }
  }

  moveLeft() {
    this.velocity.x = -this.speed;
  }

  moveRight() {
    this.velocity.x = this.speed;
  }

  stop() {
    this.velocity.x = 0;
  }
}

class Platform extends Obstacle {
  constructor(x, y, width, height) {
    super(x, y, width, height);
    this.type = 'platform';
    this.color = '#8B4513';
  }

  onCollision(other) {
    if (other.type === 'player' && other.velocity.y > 0) {
      // Land on platform
      other.y = this.y - other.height;
      other.velocity.y = 0;
      other.onGround = true;
    }
  }
}

class Spike extends Obstacle {
  constructor(x, y, width, height) {
    super(x, y, width, height);
    this.type = 'spike';
    this.color = '#696969';
  }

  render(ctx) {
    ctx.fillStyle = this.color;
    // Draw spikes as triangles
    const spikeWidth = this.width / 4;
    for (let i = 0; i < 4; i++) {
      ctx.beginPath();
      ctx.moveTo(this.x + i * spikeWidth, this.y + this.height);
      ctx.lineTo(this.x + i * spikeWidth + spikeWidth/2, this.y);
      ctx.lineTo(this.x + (i + 1) * spikeWidth, this.y + this.height);
      ctx.closePath();
      ctx.fill();
    }
  }
}

class Coin extends Collectible {
  constructor(x, y) {
    super(x, y, 20, 20, 10);
    this.color = '#FFD700';
    this.rotation = 0;
  }

  update(deltaTime) {
    super.update(deltaTime);
    this.rotation += deltaTime * 3; // Spinning effect
  }

  render(ctx) {
    ctx.save();
    ctx.translate(this.x + this.width/2, this.y + this.height/2);
    ctx.rotate(this.rotation);
    ctx.fillStyle = this.color;
    ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
    
    // Draw coin details
    ctx.strokeStyle = '#FFA500';
    ctx.lineWidth = 2;
    ctx.strokeRect(-this.width/2, -this.height/2, this.width, this.height);
    ctx.restore();
  }
}

export class SpeedRunnerGame extends GameEngine {
  constructor(canvas, config = {}) {
    const defaultConfig = {
      width: 800,
      height: 600,
      background: { color: '#87CEEB' },
      difficulty: 'medium',
      scrollSpeed: 200,
      platformSpawnRate: 1500,
      coinSpawnRate: 800,
      ...config
    };
    
    super(canvas, defaultConfig);
    
    this.runner = null;
    this.platforms = [];
    this.coins = [];
    this.spikes = [];
    this.cameraX = 0;
    this.lastPlatformSpawn = 0;
    this.lastCoinSpawn = 0;
    this.gameStarted = false;
    this.distance = 0;
    
    this.setupGame();
  }

  setupGame() {
    // Create runner
    this.runner = new Runner(100, 400);
    this.addGameObject(this.runner);
    
    // Create initial platforms
    this.createInitialPlatforms();
    
    // Bind controls
    this.setupControls();
  }

  createInitialPlatforms() {
    // Ground platform
    const ground = new Platform(0, 450, this.canvas.width * 2, 150);
    this.addGameObject(ground);
    this.platforms.push(ground);
    
    // Some initial platforms
    for (let i = 1; i < 5; i++) {
      const platform = new Platform(i * 200, 400 - Math.random() * 100, 100, 20);
      this.addGameObject(platform);
      this.platforms.push(platform);
    }
  }

  setupControls() {
    const keys = {};
    
    window.addEventListener('keydown', (e) => {
      keys[e.code] = true;
      
      if (e.code === 'Space' || e.code === 'ArrowUp') {
        e.preventDefault();
        if (!this.gameStarted) {
          this.gameStarted = true;
          this.start();
        }
        if (this.gameState === 'playing') {
          this.runner.jump();
        }
      }
    });

    window.addEventListener('keyup', (e) => {
      keys[e.code] = false;
    });

    // Handle continuous movement
    this.inputHandler = () => {
      if (this.gameState !== 'playing') return;
      
      if (keys['ArrowLeft'] || keys['KeyA']) {
        this.runner.moveLeft();
      } else if (keys['ArrowRight'] || keys['KeyD']) {
        this.runner.moveRight();
      } else {
        this.runner.stop();
      }
    };
  }

  update(deltaTime) {
    if (!this.gameStarted) return;
    
    // Handle input
    if (this.inputHandler) this.inputHandler();
    
    super.update(deltaTime);
    
    // Update camera to follow runner
    this.cameraX = this.runner.x - 200;
    
    // Update distance
    this.distance = Math.max(this.distance, this.runner.x / 10);
    
    // Spawn new platforms
    this.lastPlatformSpawn += deltaTime * 1000;
    if (this.lastPlatformSpawn >= this.config.platformSpawnRate) {
      this.spawnPlatform();
      this.lastPlatformSpawn = 0;
    }
    
    // Spawn coins
    this.lastCoinSpawn += deltaTime * 1000;
    if (this.lastCoinSpawn >= this.config.coinSpawnRate) {
      this.spawnCoin();
      this.lastCoinSpawn = 0;
    }
    
    // Remove off-screen objects
    this.cleanupObjects();
    
    // Check if runner fell off the world
    if (this.runner.y > this.canvas.height) {
      this.gameOver();
    }
  }

  spawnPlatform() {
    const x = this.cameraX + this.canvas.width + Math.random() * 200;
    const y = 200 + Math.random() * 200;
    const width = 80 + Math.random() * 120;
    
    const platform = new Platform(x, y, width, 20);
    this.addGameObject(platform);
    this.platforms.push(platform);
    
    // Sometimes add spikes
    if (Math.random() < 0.3) {
      const spike = new Spike(x + width/2 - 20, y - 20, 40, 20);
      this.addGameObject(spike);
      this.spikes.push(spike);
    }
  }

  spawnCoin() {
    const x = this.cameraX + this.canvas.width + Math.random() * 100;
    const y = 150 + Math.random() * 200;
    
    const coin = new Coin(x, y);
    this.addGameObject(coin);
    this.coins.push(coin);
  }

  cleanupObjects() {
    const cleanupThreshold = this.cameraX - 200;
    
    this.platforms = this.platforms.filter(platform => {
      if (platform.x + platform.width < cleanupThreshold) {
        this.removeGameObject(platform);
        return false;
      }
      return true;
    });
    
    this.coins = this.coins.filter(coin => {
      if (coin.x + coin.width < cleanupThreshold) {
        this.removeGameObject(coin);
        return false;
      }
      return true;
    });
    
    this.spikes = this.spikes.filter(spike => {
      if (spike.x + spike.width < cleanupThreshold) {
        this.removeGameObject(spike);
        return false;
      }
      return true;
    });
  }

  render() {
    // Save context for camera transform
    this.ctx.save();
    this.ctx.translate(-this.cameraX, 0);
    
    super.render();
    
    // Restore context
    this.ctx.restore();
    
    // Render UI (not affected by camera)
    this.renderUI();
  }

  renderUI() {
    // Distance and score
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '20px Arial';
    this.ctx.fillText(`Distance: ${Math.floor(this.distance)}m`, 20, 30);
    this.ctx.fillText(`Score: ${this.score}`, 20, 60);
    
    if (!this.gameStarted) {
      // Start screen
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Speed Runner', this.canvas.width / 2, this.canvas.height / 2 - 50);
      
      this.ctx.font = '24px Arial';
      this.ctx.fillText('Arrow Keys to Move, Space to Jump', this.canvas.width / 2, this.canvas.height / 2 + 20);
      this.ctx.textAlign = 'left';
    }
    
    if (this.gameState === 'gameOver') {
      // Game over screen
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Game Over', this.canvas.width / 2, this.canvas.height / 2 - 50);
      
      this.ctx.font = '24px Arial';
      this.ctx.fillText(`Distance: ${Math.floor(this.distance)}m`, this.canvas.width / 2, this.canvas.height / 2 + 20);
      this.ctx.fillText(`Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2 + 50);
      this.ctx.textAlign = 'left';
    }
  }

  updateParameters(newParams) {
    Object.assign(this.config, newParams);
  }

  exportGameData() {
    return {
      type: 'speedrunner',
      config: this.config,
      assets: {
        runner: this.runner.sprite || null,
        platforms: null,
        background: null
      }
    };
  }
}
