// Core Game Engine for GameGen Platform
export class GameEngine {
  constructor(canvas, gameConfig) {
    this.canvas = canvas;
    this.ctx = canvas.getContext('2d');
    this.config = gameConfig;
    this.gameState = 'menu'; // menu, playing, paused, gameOver
    this.score = 0;
    this.gameObjects = [];
    this.inputHandler = new InputHandler();
    this.assetManager = new AssetManager();
    this.physics = new PhysicsEngine();
    this.lastTime = 0;
    this.deltaTime = 0;
    
    this.setupCanvas();
    this.bindEvents();
  }

  setupCanvas() {
    this.canvas.width = this.config.width || 800;
    this.canvas.height = this.config.height || 600;
    this.ctx.imageSmoothingEnabled = false; // For pixel art games
  }

  bindEvents() {
    // Keyboard events
    window.addEventListener('keydown', (e) => this.inputHandler.handleKeyDown(e));
    window.addEventListener('keyup', (e) => this.inputHandler.handleKeyUp(e));
    
    // Mouse/Touch events
    this.canvas.addEventListener('click', (e) => this.inputHandler.handleClick(e));
    this.canvas.addEventListener('touchstart', (e) => this.inputHandler.handleTouch(e));
    this.canvas.addEventListener('touchmove', (e) => this.inputHandler.handleTouchMove(e));
  }

  start() {
    this.gameState = 'playing';
    this.lastTime = performance.now();
    this.gameLoop();
  }

  pause() {
    this.gameState = 'paused';
  }

  resume() {
    this.gameState = 'playing';
    this.lastTime = performance.now();
    this.gameLoop();
  }

  gameLoop(currentTime = performance.now()) {
    if (this.gameState !== 'playing') return;

    this.deltaTime = (currentTime - this.lastTime) / 1000;
    this.lastTime = currentTime;

    this.update(this.deltaTime);
    this.render();

    requestAnimationFrame((time) => this.gameLoop(time));
  }

  update(deltaTime) {
    // Update all game objects
    this.gameObjects.forEach(obj => {
      if (obj.update) obj.update(deltaTime);
    });

    // Handle physics
    this.physics.update(this.gameObjects, deltaTime);

    // Check collisions
    this.checkCollisions();

    // Remove dead objects
    this.gameObjects = this.gameObjects.filter(obj => !obj.dead);
  }

  render() {
    // Clear canvas
    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // Render background
    if (this.config.background) {
      this.renderBackground();
    }

    // Render all game objects
    this.gameObjects.forEach(obj => {
      if (obj.render) obj.render(this.ctx);
    });

    // Render UI
    this.renderUI();
  }

  renderBackground() {
    if (this.config.background.color) {
      this.ctx.fillStyle = this.config.background.color;
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    if (this.config.background.image) {
      const img = this.assetManager.getAsset(this.config.background.image);
      if (img) {
        this.ctx.drawImage(img, 0, 0, this.canvas.width, this.canvas.height);
      }
    }
  }

  renderUI() {
    // Score display
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '24px Arial';
    this.ctx.fillText(`Score: ${this.score}`, 20, 40);

    // Game state specific UI
    if (this.gameState === 'paused') {
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('PAUSED', this.canvas.width / 2, this.canvas.height / 2);
      this.ctx.textAlign = 'left';
    }
  }

  checkCollisions() {
    for (let i = 0; i < this.gameObjects.length; i++) {
      for (let j = i + 1; j < this.gameObjects.length; j++) {
        const objA = this.gameObjects[i];
        const objB = this.gameObjects[j];
        
        if (this.physics.checkCollision(objA, objB)) {
          this.handleCollision(objA, objB);
        }
      }
    }
  }

  handleCollision(objA, objB) {
    if (objA.onCollision) objA.onCollision(objB);
    if (objB.onCollision) objB.onCollision(objA);
  }

  addGameObject(obj) {
    this.gameObjects.push(obj);
  }

  removeGameObject(obj) {
    const index = this.gameObjects.indexOf(obj);
    if (index > -1) {
      this.gameObjects.splice(index, 1);
    }
  }

  updateScore(points) {
    this.score += points;
  }

  gameOver() {
    this.gameState = 'gameOver';
    if (this.config.onGameOver) {
      this.config.onGameOver(this.score);
    }
  }
}

// Input handling system
class InputHandler {
  constructor() {
    this.keys = {};
    this.mouse = { x: 0, y: 0, clicked: false };
    this.touch = { x: 0, y: 0, active: false };
  }

  handleKeyDown(event) {
    this.keys[event.code] = true;
    event.preventDefault();
  }

  handleKeyUp(event) {
    this.keys[event.code] = false;
    event.preventDefault();
  }

  handleClick(event) {
    const rect = event.target.getBoundingClientRect();
    this.mouse.x = event.clientX - rect.left;
    this.mouse.y = event.clientY - rect.top;
    this.mouse.clicked = true;
    setTimeout(() => this.mouse.clicked = false, 100);
  }

  handleTouch(event) {
    event.preventDefault();
    const rect = event.target.getBoundingClientRect();
    const touch = event.touches[0];
    this.touch.x = touch.clientX - rect.left;
    this.touch.y = touch.clientY - rect.top;
    this.touch.active = true;
  }

  handleTouchMove(event) {
    event.preventDefault();
    const rect = event.target.getBoundingClientRect();
    const touch = event.touches[0];
    this.touch.x = touch.clientX - rect.left;
    this.touch.y = touch.clientY - rect.top;
  }

  isKeyPressed(key) {
    return !!this.keys[key];
  }
}

// Asset management system
class AssetManager {
  constructor() {
    this.assets = new Map();
    this.loadPromises = new Map();
  }

  async loadAsset(name, url, type = 'image') {
    if (this.assets.has(name)) {
      return this.assets.get(name);
    }

    if (this.loadPromises.has(name)) {
      return this.loadPromises.get(name);
    }

    const promise = this.loadAssetFile(url, type);
    this.loadPromises.set(name, promise);

    try {
      const asset = await promise;
      this.assets.set(name, asset);
      this.loadPromises.delete(name);
      return asset;
    } catch (error) {
      this.loadPromises.delete(name);
      throw error;
    }
  }

  loadAssetFile(url, type) {
    return new Promise((resolve, reject) => {
      if (type === 'image') {
        const img = new Image();
        img.onload = () => resolve(img);
        img.onerror = reject;
        img.src = url;
      } else if (type === 'audio') {
        const audio = new Audio();
        audio.oncanplaythrough = () => resolve(audio);
        audio.onerror = reject;
        audio.src = url;
      }
    });
  }

  getAsset(name) {
    return this.assets.get(name);
  }
}

// Basic physics engine
class PhysicsEngine {
  constructor() {
    this.gravity = 980; // pixels per second squared
  }

  update(gameObjects, deltaTime) {
    gameObjects.forEach(obj => {
      if (obj.physics) {
        this.updatePhysics(obj, deltaTime);
      }
    });
  }

  updatePhysics(obj, deltaTime) {
    // Apply gravity
    if (obj.physics.gravity) {
      obj.velocity.y += this.gravity * deltaTime;
    }

    // Update position based on velocity
    obj.x += obj.velocity.x * deltaTime;
    obj.y += obj.velocity.y * deltaTime;

    // Apply friction
    if (obj.physics.friction) {
      obj.velocity.x *= (1 - obj.physics.friction * deltaTime);
    }
  }

  checkCollision(objA, objB) {
    if (!objA.bounds || !objB.bounds) return false;

    return objA.x < objB.x + objB.bounds.width &&
           objA.x + objA.bounds.width > objB.x &&
           objA.y < objB.y + objB.bounds.height &&
           objA.y + objA.bounds.height > objB.y;
  }
}
