import { GameEngine } from '../../services/gameEngine.js';
import { GameObject } from '../../services/GameObject.js';

// Match-3 specific classes
class Gem extends GameObject {
  constructor(x, y, gridX, gridY, type) {
    super(x, y, 40, 40);
    this.gridX = gridX;
    this.gridY = gridY;
    this.gemType = type;
    this.selected = false;
    this.matched = false;
    this.falling = false;
    this.targetY = y;
    this.fallSpeed = 400;
    
    // Gem colors
    const colors = ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'];
    this.color = colors[type];
  }

  update(deltaTime) {
    super.update(deltaTime);
    
    if (this.falling && this.y < this.targetY) {
      this.y += this.fallSpeed * deltaTime;
      if (this.y >= this.targetY) {
        this.y = this.targetY;
        this.falling = false;
      }
    }
  }

  render(ctx) {
    if (this.matched) return;
    
    // Draw gem
    ctx.fillStyle = this.color;
    ctx.fillRect(this.x + 2, this.y + 2, this.width - 4, this.height - 4);
    
    // Draw gem highlight
    ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
    ctx.fillRect(this.x + 4, this.y + 4, this.width - 8, this.height - 8);
    
    // Draw selection border
    if (this.selected) {
      ctx.strokeStyle = '#FFFFFF';
      ctx.lineWidth = 3;
      ctx.strokeRect(this.x, this.y, this.width, this.height);
    }
    
    // Draw gem shape based on type
    ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
    const centerX = this.x + this.width / 2;
    const centerY = this.y + this.height / 2;
    
    switch (this.gemType) {
      case 0: // Circle
        ctx.beginPath();
        ctx.arc(centerX, centerY, 8, 0, 2 * Math.PI);
        ctx.fill();
        break;
      case 1: // Square
        ctx.fillRect(centerX - 6, centerY - 6, 12, 12);
        break;
      case 2: // Triangle
        ctx.beginPath();
        ctx.moveTo(centerX, centerY - 8);
        ctx.lineTo(centerX - 8, centerY + 6);
        ctx.lineTo(centerX + 8, centerY + 6);
        ctx.closePath();
        ctx.fill();
        break;
      case 3: // Diamond
        ctx.beginPath();
        ctx.moveTo(centerX, centerY - 8);
        ctx.lineTo(centerX + 8, centerY);
        ctx.lineTo(centerX, centerY + 8);
        ctx.lineTo(centerX - 8, centerY);
        ctx.closePath();
        ctx.fill();
        break;
      case 4: // Star
        this.drawStar(ctx, centerX, centerY, 5, 8, 4);
        break;
      case 5: // Hexagon
        this.drawHexagon(ctx, centerX, centerY, 8);
        break;
    }
  }

  drawStar(ctx, x, y, spikes, outerRadius, innerRadius) {
    let rot = Math.PI / 2 * 3;
    let step = Math.PI / spikes;
    
    ctx.beginPath();
    ctx.moveTo(x, y - outerRadius);
    
    for (let i = 0; i < spikes; i++) {
      ctx.lineTo(x + Math.cos(rot) * outerRadius, y + Math.sin(rot) * outerRadius);
      rot += step;
      ctx.lineTo(x + Math.cos(rot) * innerRadius, y + Math.sin(rot) * innerRadius);
      rot += step;
    }
    
    ctx.lineTo(x, y - outerRadius);
    ctx.closePath();
    ctx.fill();
  }

  drawHexagon(ctx, x, y, radius) {
    ctx.beginPath();
    for (let i = 0; i < 6; i++) {
      const angle = (i * Math.PI) / 3;
      const px = x + radius * Math.cos(angle);
      const py = y + radius * Math.sin(angle);
      if (i === 0) ctx.moveTo(px, py);
      else ctx.lineTo(px, py);
    }
    ctx.closePath();
    ctx.fill();
  }

  setGridPosition(gridX, gridY) {
    this.gridX = gridX;
    this.gridY = gridY;
  }

  startFalling(targetY) {
    this.falling = true;
    this.targetY = targetY;
  }
}

export class Match3Game extends GameEngine {
  constructor(canvas, config = {}) {
    const defaultConfig = {
      width: 800,
      height: 600,
      background: { color: '#2F2F2F' },
      difficulty: 'medium',
      gridWidth: 8,
      gridHeight: 8,
      gemTypes: 6,
      scoreMultiplier: 10,
      ...config
    };
    
    super(canvas, defaultConfig);
    
    this.grid = [];
    this.gems = [];
    this.selectedGem = null;
    this.gameStarted = false;
    this.moves = 0;
    this.targetScore = 1000;
    this.gridOffsetX = 200;
    this.gridOffsetY = 100;
    this.gemSize = 40;
    this.animating = false;
    
    this.setupGame();
  }

  setupGame() {
    this.initializeGrid();
    this.setupControls();
  }

  initializeGrid() {
    // Initialize empty grid
    this.grid = [];
    for (let y = 0; y < this.config.gridHeight; y++) {
      this.grid[y] = [];
      for (let x = 0; x < this.config.gridWidth; x++) {
        this.grid[y][x] = null;
      }
    }
    
    // Fill grid with gems
    for (let y = 0; y < this.config.gridHeight; y++) {
      for (let x = 0; x < this.config.gridWidth; x++) {
        this.createGem(x, y);
      }
    }
    
    // Remove initial matches
    this.removeMatches();
    this.fillEmptySpaces();
  }

  createGem(gridX, gridY) {
    const x = this.gridOffsetX + gridX * this.gemSize;
    const y = this.gridOffsetY + gridY * this.gemSize;
    
    let gemType;
    do {
      gemType = Math.floor(Math.random() * this.config.gemTypes);
    } while (this.wouldCreateMatch(gridX, gridY, gemType));
    
    const gem = new Gem(x, y, gridX, gridY, gemType);
    this.grid[gridY][gridX] = gem;
    this.gems.push(gem);
    this.addGameObject(gem);
    
    return gem;
  }

  wouldCreateMatch(gridX, gridY, gemType) {
    // Check horizontal match
    let horizontalCount = 1;
    
    // Check left
    for (let x = gridX - 1; x >= 0; x--) {
      if (this.grid[gridY][x] && this.grid[gridY][x].gemType === gemType) {
        horizontalCount++;
      } else {
        break;
      }
    }
    
    // Check right
    for (let x = gridX + 1; x < this.config.gridWidth; x++) {
      if (this.grid[gridY][x] && this.grid[gridY][x].gemType === gemType) {
        horizontalCount++;
      } else {
        break;
      }
    }
    
    if (horizontalCount >= 3) return true;
    
    // Check vertical match
    let verticalCount = 1;
    
    // Check up
    for (let y = gridY - 1; y >= 0; y--) {
      if (this.grid[y][gridX] && this.grid[y][gridX].gemType === gemType) {
        verticalCount++;
      } else {
        break;
      }
    }
    
    // Check down
    for (let y = gridY + 1; y < this.config.gridHeight; y++) {
      if (this.grid[y][gridX] && this.grid[y][gridX].gemType === gemType) {
        verticalCount++;
      } else {
        break;
      }
    }
    
    return verticalCount >= 3;
  }

  setupControls() {
    this.canvas.addEventListener('click', (e) => {
      if (!this.gameStarted) {
        this.gameStarted = true;
        this.start();
        return;
      }
      
      if (this.gameState !== 'playing' || this.animating) return;
      
      const rect = this.canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      this.handleClick(x, y);
    });
  }

  handleClick(x, y) {
    const gridX = Math.floor((x - this.gridOffsetX) / this.gemSize);
    const gridY = Math.floor((y - this.gridOffsetY) / this.gemSize);
    
    if (gridX < 0 || gridX >= this.config.gridWidth || 
        gridY < 0 || gridY >= this.config.gridHeight) {
      return;
    }
    
    const clickedGem = this.grid[gridY][gridX];
    if (!clickedGem) return;
    
    if (!this.selectedGem) {
      // Select gem
      this.selectedGem = clickedGem;
      clickedGem.selected = true;
    } else if (this.selectedGem === clickedGem) {
      // Deselect gem
      this.selectedGem.selected = false;
      this.selectedGem = null;
    } else if (this.areAdjacent(this.selectedGem, clickedGem)) {
      // Swap gems
      this.swapGems(this.selectedGem, clickedGem);
    } else {
      // Select new gem
      this.selectedGem.selected = false;
      this.selectedGem = clickedGem;
      clickedGem.selected = true;
    }
  }

  areAdjacent(gem1, gem2) {
    const dx = Math.abs(gem1.gridX - gem2.gridX);
    const dy = Math.abs(gem1.gridY - gem2.gridY);
    return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
  }

  swapGems(gem1, gem2) {
    // Swap positions in grid
    this.grid[gem1.gridY][gem1.gridX] = gem2;
    this.grid[gem2.gridY][gem2.gridX] = gem1;
    
    // Swap grid coordinates
    const tempGridX = gem1.gridX;
    const tempGridY = gem1.gridY;
    gem1.setGridPosition(gem2.gridX, gem2.gridY);
    gem2.setGridPosition(tempGridX, tempGridY);
    
    // Update visual positions
    gem1.x = this.gridOffsetX + gem1.gridX * this.gemSize;
    gem1.y = this.gridOffsetY + gem1.gridY * this.gemSize;
    gem2.x = this.gridOffsetX + gem2.gridX * this.gemSize;
    gem2.y = this.gridOffsetY + gem2.gridY * this.gemSize;
    
    // Check for matches
    if (this.hasMatches()) {
      this.moves++;
      this.processMatches();
    } else {
      // Swap back if no matches
      this.swapGems(gem1, gem2);
    }
    
    // Deselect
    gem1.selected = false;
    gem2.selected = false;
    this.selectedGem = null;
  }

  hasMatches() {
    for (let y = 0; y < this.config.gridHeight; y++) {
      for (let x = 0; x < this.config.gridWidth; x++) {
        if (this.grid[y][x] && this.wouldCreateMatch(x, y, this.grid[y][x].gemType)) {
          return true;
        }
      }
    }
    return false;
  }

  processMatches() {
    this.animating = true;
    this.removeMatches();
    
    setTimeout(() => {
      this.fillEmptySpaces();
      setTimeout(() => {
        if (this.hasMatches()) {
          this.processMatches();
        } else {
          this.animating = false;
          this.checkGameEnd();
        }
      }, 500);
    }, 300);
  }

  removeMatches() {
    const toRemove = [];
    
    // Find all matches
    for (let y = 0; y < this.config.gridHeight; y++) {
      for (let x = 0; x < this.config.gridWidth; x++) {
        if (this.grid[y][x] && this.wouldCreateMatch(x, y, this.grid[y][x].gemType)) {
          toRemove.push(this.grid[y][x]);
        }
      }
    }
    
    // Remove matched gems
    toRemove.forEach(gem => {
      this.updateScore(this.config.scoreMultiplier);
      this.grid[gem.gridY][gem.gridX] = null;
      this.removeGameObject(gem);
      const index = this.gems.indexOf(gem);
      if (index > -1) this.gems.splice(index, 1);
    });
  }

  fillEmptySpaces() {
    // Drop existing gems
    for (let x = 0; x < this.config.gridWidth; x++) {
      let writeIndex = this.config.gridHeight - 1;
      
      for (let y = this.config.gridHeight - 1; y >= 0; y--) {
        if (this.grid[y][x]) {
          if (y !== writeIndex) {
            this.grid[writeIndex][x] = this.grid[y][x];
            this.grid[y][x] = null;
            
            const gem = this.grid[writeIndex][x];
            gem.setGridPosition(x, writeIndex);
            const newY = this.gridOffsetY + writeIndex * this.gemSize;
            gem.startFalling(newY);
          }
          writeIndex--;
        }
      }
      
      // Create new gems for empty spaces
      for (let y = writeIndex; y >= 0; y--) {
        const gem = this.createGem(x, y);
        gem.y = this.gridOffsetY - (writeIndex - y + 1) * this.gemSize;
        gem.startFalling(this.gridOffsetY + y * this.gemSize);
      }
    }
  }

  checkGameEnd() {
    if (this.score >= this.targetScore) {
      this.gameState = 'won';
    }
    // Add other win/lose conditions as needed
  }

  renderUI() {
    super.renderUI();
    
    // Game info
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '20px Arial';
    this.ctx.fillText(`Moves: ${this.moves}`, 20, 90);
    this.ctx.fillText(`Target: ${this.targetScore}`, 20, 120);
    
    if (!this.gameStarted) {
      // Start screen
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Match-3', this.canvas.width / 2, this.canvas.height / 2 - 50);
      
      this.ctx.font = '24px Arial';
      this.ctx.fillText('Click to Start!', this.canvas.width / 2, this.canvas.height / 2 + 20);
      this.ctx.textAlign = 'left';
    }
  }

  updateParameters(newParams) {
    Object.assign(this.config, newParams);
  }

  exportGameData() {
    return {
      type: 'match3',
      config: this.config,
      assets: {
        gems: null,
        background: null
      }
    };
  }
}
