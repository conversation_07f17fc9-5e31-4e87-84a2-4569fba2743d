import geminiService from './geminiService.js';

class ParameterTuner {
  constructor() {
    this.gameParameters = {
      'flappybird': {
        difficulty: 'medium',
        pipeGap: 150,
        pipeSpeed: 200,
        pipeSpawnRate: 2000,
        gravity: 980,
        jumpForce: -400,
        birdSize: { width: 40, height: 30 }
      },
      'speedrunner': {
        difficulty: 'medium',
        scrollSpeed: 200,
        platformSpawnRate: 1500,
        coinSpawnRate: 800,
        jumpForce: -500,
        runSpeed: 300,
        gravity: 980
      },
      'whackthemole': {
        difficulty: 'medium',
        moleSpawnRate: 1500,
        gameTime: 60000,
        maxActiveMoles: 3,
        moleVisibleTime: 2000,
        gridSize: { rows: 3, cols: 3 }
      },
      'match3': {
        difficulty: 'medium',
        gridSize: { width: 8, height: 8 },
        gemTypes: 6,
        scoreMultiplier: 10,
        targetScore: 1000,
        timeLimit: 300000
      },
      'crossyroad': {
        difficulty: 'medium',
        laneSpeed: 100,
        carSpawnRate: 2000,
        logSpawnRate: 3000,
        characterSpeed: 200,
        laneWidth: 40
      }
    };

    this.difficultyPresets = {
      'easy': {
        description: 'Relaxed gameplay suitable for beginners',
        modifiers: {
          speed: 0.7,
          spawnRate: 1.3,
          obstacles: 0.8,
          timeLimit: 1.5
        }
      },
      'medium': {
        description: 'Balanced gameplay for average players',
        modifiers: {
          speed: 1.0,
          spawnRate: 1.0,
          obstacles: 1.0,
          timeLimit: 1.0
        }
      },
      'hard': {
        description: 'Challenging gameplay for experienced players',
        modifiers: {
          speed: 1.4,
          spawnRate: 0.7,
          obstacles: 1.3,
          timeLimit: 0.8
        }
      },
      'extreme': {
        description: 'Very difficult gameplay for experts',
        modifiers: {
          speed: 1.8,
          spawnRate: 0.5,
          obstacles: 1.6,
          timeLimit: 0.6
        }
      }
    };

    this.tuningHistory = [];
  }

  async tuneParameters(gameType, userRequest, currentParams = null) {
    try {
      const baseParams = currentParams || this.gameParameters[gameType];
      if (!baseParams) {
        throw new Error(`Unknown game type: ${gameType}`);
      }

      console.log(`Tuning parameters for ${gameType}: "${userRequest}"`);

      // Use Gemini to interpret the user request
      const tuningResult = await geminiService.tuneGameParameters(
        userRequest,
        baseParams,
        gameType
      );

      // Validate and apply the changes
      const validatedParams = this.validateParameters(gameType, tuningResult);
      
      // Store tuning history
      this.tuningHistory.push({
        gameType,
        userRequest,
        originalParams: { ...baseParams },
        newParams: { ...validatedParams },
        timestamp: Date.now()
      });

      return {
        success: true,
        parameters: validatedParams,
        changes: this.getParameterChanges(baseParams, validatedParams),
        explanation: this.generateExplanation(userRequest, baseParams, validatedParams)
      };

    } catch (error) {
      console.error('Error tuning parameters:', error);
      return {
        success: false,
        error: error.message,
        fallbackParams: this.gameParameters[gameType]
      };
    }
  }

  validateParameters(gameType, params) {
    const validated = { ...params };
    const constraints = this.getParameterConstraints(gameType);

    // Apply constraints to ensure parameters are within valid ranges
    Object.keys(constraints).forEach(key => {
      if (validated[key] !== undefined) {
        const constraint = constraints[key];
        
        if (typeof validated[key] === 'number') {
          validated[key] = Math.max(constraint.min, Math.min(constraint.max, validated[key]));
        }
      }
    });

    return validated;
  }

  getParameterConstraints(gameType) {
    const constraints = {
      'flappybird': {
        pipeGap: { min: 80, max: 300 },
        pipeSpeed: { min: 50, max: 500 },
        pipeSpawnRate: { min: 800, max: 5000 },
        gravity: { min: 200, max: 2000 },
        jumpForce: { min: -800, max: -200 }
      },
      'speedrunner': {
        scrollSpeed: { min: 50, max: 400 },
        platformSpawnRate: { min: 500, max: 3000 },
        coinSpawnRate: { min: 300, max: 2000 },
        jumpForce: { min: -800, max: -200 },
        runSpeed: { min: 100, max: 600 }
      },
      'whackthemole': {
        moleSpawnRate: { min: 500, max: 3000 },
        gameTime: { min: 30000, max: 180000 },
        maxActiveMoles: { min: 1, max: 6 },
        moleVisibleTime: { min: 800, max: 4000 }
      },
      'match3': {
        gemTypes: { min: 4, max: 8 },
        scoreMultiplier: { min: 1, max: 50 },
        targetScore: { min: 100, max: 10000 },
        timeLimit: { min: 60000, max: 600000 }
      },
      'crossyroad': {
        laneSpeed: { min: 30, max: 300 },
        carSpawnRate: { min: 800, max: 4000 },
        logSpawnRate: { min: 1000, max: 5000 },
        characterSpeed: { min: 100, max: 400 }
      }
    };

    return constraints[gameType] || {};
  }

  getParameterChanges(oldParams, newParams) {
    const changes = [];
    
    Object.keys(newParams).forEach(key => {
      if (oldParams[key] !== newParams[key]) {
        changes.push({
          parameter: key,
          oldValue: oldParams[key],
          newValue: newParams[key],
          change: this.calculateChange(oldParams[key], newParams[key])
        });
      }
    });

    return changes;
  }

  calculateChange(oldValue, newValue) {
    if (typeof oldValue === 'number' && typeof newValue === 'number') {
      const percentChange = ((newValue - oldValue) / oldValue) * 100;
      return {
        type: 'numeric',
        absolute: newValue - oldValue,
        percentage: Math.round(percentChange * 100) / 100
      };
    }
    
    return {
      type: 'value',
      description: `Changed from ${oldValue} to ${newValue}`
    };
  }

  generateExplanation(userRequest, oldParams, newParams) {
    const changes = this.getParameterChanges(oldParams, newParams);
    
    if (changes.length === 0) {
      return "No parameter changes were made based on your request.";
    }

    let explanation = `Based on your request "${userRequest}", I made the following adjustments:\n\n`;
    
    changes.forEach(change => {
      const { parameter, oldValue, newValue, change: changeInfo } = change;
      
      if (changeInfo.type === 'numeric') {
        const direction = changeInfo.percentage > 0 ? 'increased' : 'decreased';
        explanation += `• ${parameter}: ${direction} from ${oldValue} to ${newValue} (${Math.abs(changeInfo.percentage)}% change)\n`;
      } else {
        explanation += `• ${parameter}: ${changeInfo.description}\n`;
      }
    });

    return explanation;
  }

  applyDifficultyPreset(gameType, difficulty) {
    const baseParams = { ...this.gameParameters[gameType] };
    const preset = this.difficultyPresets[difficulty];
    
    if (!preset) {
      throw new Error(`Unknown difficulty preset: ${difficulty}`);
    }

    const modifiedParams = { ...baseParams };
    modifiedParams.difficulty = difficulty;

    // Apply modifiers based on parameter type
    Object.keys(baseParams).forEach(key => {
      const value = baseParams[key];
      
      if (typeof value === 'number') {
        if (key.includes('speed') || key.includes('Speed')) {
          modifiedParams[key] = Math.round(value * preset.modifiers.speed);
        } else if (key.includes('spawn') || key.includes('Spawn')) {
          modifiedParams[key] = Math.round(value * preset.modifiers.spawnRate);
        } else if (key.includes('time') || key.includes('Time')) {
          modifiedParams[key] = Math.round(value * preset.modifiers.timeLimit);
        }
      }
    });

    return this.validateParameters(gameType, modifiedParams);
  }

  async interpretNaturalLanguage(userRequest, gameType) {
    // Common patterns and their parameter mappings
    const patterns = {
      'easier|easy|simple|beginner': () => this.applyDifficultyPreset(gameType, 'easy'),
      'harder|hard|difficult|challenging': () => this.applyDifficultyPreset(gameType, 'hard'),
      'faster|speed up|quicker': (params) => this.adjustSpeed(params, 1.3),
      'slower|slow down': (params) => this.adjustSpeed(params, 0.7),
      'more obstacles|more enemies': (params) => this.adjustObstacles(params, 1.4),
      'fewer obstacles|less enemies': (params) => this.adjustObstacles(params, 0.7),
      'bigger gaps|larger gaps': (params) => this.adjustGaps(params, 1.3),
      'smaller gaps|tighter gaps': (params) => this.adjustGaps(params, 0.8),
      'longer game|more time': (params) => this.adjustTime(params, 1.5),
      'shorter game|less time': (params) => this.adjustTime(params, 0.7)
    };

    const baseParams = this.gameParameters[gameType];
    
    // Check for pattern matches
    for (const [pattern, modifier] of Object.entries(patterns)) {
      const regex = new RegExp(pattern, 'i');
      if (regex.test(userRequest)) {
        if (typeof modifier === 'function') {
          return modifier(baseParams);
        }
      }
    }

    // If no pattern matches, use AI interpretation
    return await this.tuneParameters(gameType, userRequest, baseParams);
  }

  adjustSpeed(params, multiplier) {
    const adjusted = { ...params };
    Object.keys(adjusted).forEach(key => {
      if (key.includes('speed') || key.includes('Speed')) {
        adjusted[key] = Math.round(adjusted[key] * multiplier);
      }
    });
    return adjusted;
  }

  adjustObstacles(params, multiplier) {
    const adjusted = { ...params };
    Object.keys(adjusted).forEach(key => {
      if (key.includes('spawn') || key.includes('Spawn')) {
        adjusted[key] = Math.round(adjusted[key] / multiplier); // Inverse for spawn rates
      }
    });
    return adjusted;
  }

  adjustGaps(params, multiplier) {
    const adjusted = { ...params };
    if (adjusted.pipeGap) adjusted.pipeGap = Math.round(adjusted.pipeGap * multiplier);
    return adjusted;
  }

  adjustTime(params, multiplier) {
    const adjusted = { ...params };
    Object.keys(adjusted).forEach(key => {
      if (key.includes('time') || key.includes('Time')) {
        adjusted[key] = Math.round(adjusted[key] * multiplier);
      }
    });
    return adjusted;
  }

  getGameParameters(gameType) {
    return { ...this.gameParameters[gameType] };
  }

  getTuningHistory() {
    return [...this.tuningHistory];
  }

  clearHistory() {
    this.tuningHistory = [];
  }

  exportParameters(gameType, params) {
    return {
      gameType,
      parameters: params,
      exportedAt: Date.now(),
      version: '1.0'
    };
  }
}

export default new ParameterTuner();
