# GameGen Setup Guide

This guide will help you set up and run the GameGen platform locally.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (version 18 or higher)
- **npm** (comes with Node.js)
- **Git** (for cloning the repository)
- **Modern web browser** (Chrome, Firefox, Safari, or Edge)

## Step-by-Step Setup

### 1. <PERSON>lone the Repository

```bash
git clone <repository-url>
cd gamegen
```

### 2. Install Dependencies

```bash
npm install
```

This will install all required packages including:
- React.js and Vite
- Lucide React (for icons)
- Google Generative AI SDK
- JSZip and FileSaver (for game export)
- Canvas Confetti (for effects)

### 3. Configure Environment Variables

#### Get a Gemini API Key

1. Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated API key

#### Set Up Environment File

```bash
# Copy the example environment file
cp .env.example .env
```

Edit the `.env` file and add your API key:

```env
VITE_GEMINI_API_KEY=your_actual_api_key_here
VITE_APP_NAME=GameGen
VITE_APP_VERSION=1.0.0
```

### 4. Start the Development Server

```bash
npm run dev
```

The application will start on `http://localhost:5173/`

### 5. Open in Browser

Navigate to `http://localhost:5173/` in your web browser.

## Verification Steps

### Test Basic Functionality

1. **Template Selection**: You should see 5 game templates
2. **Game Preview**: Click on a template to see the preview
3. **Navigation**: Use the progress indicator to see current step

### Test AI Integration

1. **Asset Generation**: Try generating visual assets
2. **Parameter Tuning**: Use natural language to modify game settings
3. **Audio Generation**: Generate background music and sound effects

### Test Export Functionality

1. **Complete Wizard**: Go through all 4 steps
2. **Export Game**: Download the ZIP file
3. **Test Exported Game**: Extract and open `index.html`

## Troubleshooting

### Common Issues

#### "API Key Not Found" Error
- Check that your `.env` file exists in the root directory
- Verify the API key is correctly formatted
- Restart the development server after changing environment variables

#### "Module Not Found" Errors
- Run `npm install` again
- Delete `node_modules` and `package-lock.json`, then run `npm install`
- Check that you're in the correct directory

#### Games Not Loading
- Check browser console for JavaScript errors
- Ensure all game files are properly imported
- Try refreshing the page

#### Export Not Working
- Check browser's download settings
- Ensure pop-ups are allowed for the site
- Try using a different browser

### Performance Issues

#### Slow Asset Generation
- Check your internet connection
- Verify Gemini API quotas
- Try simpler prompts

#### Browser Compatibility
- Use a modern browser (Chrome 90+, Firefox 88+, Safari 14+)
- Enable JavaScript
- Clear browser cache if needed

## Development Commands

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run linting
npm run lint
```

## Project Structure

```
gamegen/
├── public/                 # Static assets
├── src/
│   ├── components/
│   │   ├── wizard/        # 4-step creation wizard
│   │   ├── games/         # Game template implementations
│   │   └── ui/            # Reusable UI components
│   ├── services/          # Core services
│   │   ├── geminiService.js    # AI integration
│   │   ├── assetGenerator.js   # Asset creation
│   │   ├── parameterTuner.js   # Gameplay tuning
│   │   ├── audioManager.js     # Audio generation
│   │   ├── gameEngine.js       # Core game engine
│   │   └── exportService.js    # Game export
│   ├── utils/             # Helper functions
│   ├── App.jsx            # Main application component
│   └── main.jsx           # Application entry point
├── .env                   # Environment variables (create this)
├── .env.example           # Environment template
├── package.json           # Dependencies and scripts
└── vite.config.js         # Vite configuration
```

## Environment Variables Reference

| Variable | Required | Description |
|----------|----------|-------------|
| `VITE_GEMINI_API_KEY` | Yes | Google Gemini API key for AI features |
| `VITE_APP_NAME` | No | Application name (default: GameGen) |
| `VITE_APP_VERSION` | No | Application version (default: 1.0.0) |

## API Usage and Limits

### Gemini API
- **Free Tier**: 60 requests per minute
- **Rate Limits**: May vary based on your Google Cloud setup
- **Billing**: Check Google Cloud Console for usage

### Best Practices
- Use descriptive but concise prompts
- Cache generated assets when possible
- Monitor API usage in Google Cloud Console

## Security Notes

- Never commit your `.env` file to version control
- Keep your API keys secure and private
- Regenerate API keys if compromised
- Use environment-specific API keys for different deployments

## Next Steps

Once you have GameGen running:

1. **Explore Templates**: Try each of the 5 game templates
2. **Experiment with AI**: Test different visual styles and prompts
3. **Create a Game**: Complete the full wizard workflow
4. **Export and Share**: Download and test your created games
5. **Customize Further**: Modify the code to add new features

## Getting Help

If you encounter issues:

1. Check this setup guide first
2. Look at the browser console for error messages
3. Verify all prerequisites are met
4. Try the troubleshooting steps above
5. Create an issue on GitHub with detailed error information

## Contributing

To contribute to GameGen:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Happy game creating! 🎮**
