import { GameEngine } from '../../services/gameEngine.js';
import { GameObject } from '../../services/GameObject.js';

// Whack-the-Mole specific classes
class Mole extends GameObject {
  constructor(x, y, holeIndex) {
    super(x, y, 80, 80);
    this.type = 'mole';
    this.holeIndex = holeIndex;
    this.state = 'hidden'; // hidden, emerging, visible, hiding
    this.visibleTime = 0;
    this.maxVisibleTime = 2000; // 2 seconds
    this.animationTime = 0;
    this.animationDuration = 300; // 0.3 seconds for emerge/hide
    this.color = '#8B4513';
    this.points = 10;
    this.clicked = false;
  }

  update(deltaTime) {
    super.update(deltaTime);
    
    const deltaMs = deltaTime * 1000;
    
    switch (this.state) {
      case 'emerging':
        this.animationTime += deltaMs;
        if (this.animationTime >= this.animationDuration) {
          this.state = 'visible';
          this.animationTime = 0;
        }
        break;
        
      case 'visible':
        this.visibleTime += deltaMs;
        if (this.visibleTime >= this.maxVisibleTime || this.clicked) {
          this.state = 'hiding';
          this.animationTime = 0;
        }
        break;
        
      case 'hiding':
        this.animationTime += deltaMs;
        if (this.animationTime >= this.animationDuration) {
          this.state = 'hidden';
          this.reset();
        }
        break;
    }
  }

  render(ctx) {
    if (this.state === 'hidden') return;
    
    // Calculate animation offset
    let yOffset = 0;
    if (this.state === 'emerging') {
      yOffset = this.height * (1 - this.animationTime / this.animationDuration);
    } else if (this.state === 'hiding') {
      yOffset = this.height * (this.animationTime / this.animationDuration);
    }
    
    // Draw hole
    ctx.fillStyle = '#2F4F2F';
    ctx.beginPath();
    ctx.ellipse(this.x + this.width/2, this.y + this.height, this.width/2, 20, 0, 0, 2 * Math.PI);
    ctx.fill();
    
    // Draw mole
    const moleY = this.y + yOffset;
    ctx.fillStyle = this.clicked ? '#FF6B6B' : this.color;
    
    // Mole body
    ctx.beginPath();
    ctx.ellipse(this.x + this.width/2, moleY + this.height/2, this.width/3, this.height/3, 0, 0, 2 * Math.PI);
    ctx.fill();
    
    // Mole head
    ctx.beginPath();
    ctx.ellipse(this.x + this.width/2, moleY + this.height/4, this.width/4, this.height/4, 0, 0, 2 * Math.PI);
    ctx.fill();
    
    // Eyes
    ctx.fillStyle = '#000';
    ctx.fillRect(this.x + this.width/2 - 8, moleY + this.height/4 - 5, 4, 4);
    ctx.fillRect(this.x + this.width/2 + 4, moleY + this.height/4 - 5, 4, 4);
    
    // Nose
    ctx.fillStyle = '#FF69B4';
    ctx.fillRect(this.x + this.width/2 - 2, moleY + this.height/4 + 2, 4, 3);
  }

  emerge() {
    if (this.state === 'hidden') {
      this.state = 'emerging';
      this.animationTime = 0;
      this.visibleTime = 0;
      this.clicked = false;
    }
  }

  whack() {
    if (this.state === 'visible' && !this.clicked) {
      this.clicked = true;
      return this.points;
    }
    return 0;
  }

  reset() {
    this.visibleTime = 0;
    this.animationTime = 0;
    this.clicked = false;
  }

  isClickable() {
    return this.state === 'visible' && !this.clicked;
  }

  getBounds() {
    if (this.state === 'hidden') return null;
    return super.getBounds();
  }
}

class Hammer extends GameObject {
  constructor() {
    super(0, 0, 40, 60);
    this.type = 'hammer';
    this.visible = false;
    this.animationTime = 0;
    this.animationDuration = 200;
  }

  update(deltaTime) {
    super.update(deltaTime);
    
    if (this.visible) {
      this.animationTime += deltaTime * 1000;
      if (this.animationTime >= this.animationDuration) {
        this.visible = false;
        this.animationTime = 0;
      }
    }
  }

  render(ctx) {
    if (!this.visible) return;
    
    // Hammer handle
    ctx.fillStyle = '#8B4513';
    ctx.fillRect(this.x + this.width/2 - 3, this.y + 20, 6, 40);
    
    // Hammer head
    ctx.fillStyle = '#696969';
    ctx.fillRect(this.x, this.y, this.width, 25);
    
    // Animation effect (scale down as animation progresses)
    const scale = 1 - (this.animationTime / this.animationDuration) * 0.3;
    ctx.save();
    ctx.translate(this.x + this.width/2, this.y + this.height/2);
    ctx.scale(scale, scale);
    ctx.translate(-this.width/2, -this.height/2);
    ctx.restore();
  }

  show(x, y) {
    this.x = x - this.width/2;
    this.y = y - this.height/2;
    this.visible = true;
    this.animationTime = 0;
  }
}

export class WhackTheMoleGame extends GameEngine {
  constructor(canvas, config = {}) {
    const defaultConfig = {
      width: 800,
      height: 600,
      background: { color: '#90EE90' },
      difficulty: 'medium',
      moleSpawnRate: 1500,
      gameTime: 60000, // 60 seconds
      gridRows: 3,
      gridCols: 3,
      ...config
    };
    
    super(canvas, defaultConfig);
    
    this.moles = [];
    this.hammer = new Hammer();
    this.lastMoleSpawn = 0;
    this.gameTimeLeft = this.config.gameTime;
    this.gameStarted = false;
    this.activeMoles = 0;
    this.maxActiveMoles = 3;
    
    this.setupGame();
  }

  setupGame() {
    // Create mole grid
    const startX = 100;
    const startY = 150;
    const spacingX = 200;
    const spacingY = 150;
    
    for (let row = 0; row < this.config.gridRows; row++) {
      for (let col = 0; col < this.config.gridCols; col++) {
        const x = startX + col * spacingX;
        const y = startY + row * spacingY;
        const mole = new Mole(x, y, row * this.config.gridCols + col);
        this.moles.push(mole);
        this.addGameObject(mole);
      }
    }
    
    this.addGameObject(this.hammer);
    this.setupControls();
  }

  setupControls() {
    this.canvas.addEventListener('click', (e) => {
      if (!this.gameStarted) {
        this.gameStarted = true;
        this.start();
        return;
      }
      
      if (this.gameState !== 'playing') return;
      
      const rect = this.canvas.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      this.handleClick(x, y);
    });

    this.canvas.addEventListener('touchstart', (e) => {
      e.preventDefault();
      if (!this.gameStarted) {
        this.gameStarted = true;
        this.start();
        return;
      }
      
      if (this.gameState !== 'playing') return;
      
      const rect = this.canvas.getBoundingClientRect();
      const touch = e.touches[0];
      const x = touch.clientX - rect.left;
      const y = touch.clientY - rect.top;
      
      this.handleClick(x, y);
    });
  }

  handleClick(x, y) {
    // Show hammer at click position
    this.hammer.show(x, y);
    
    // Check if any mole was clicked
    for (const mole of this.moles) {
      if (mole.isClickable()) {
        const bounds = mole.getBounds();
        if (bounds && x >= bounds.left && x <= bounds.right && 
            y >= bounds.top && y <= bounds.bottom) {
          const points = mole.whack();
          this.updateScore(points);
          break;
        }
      }
    }
  }

  update(deltaTime) {
    if (!this.gameStarted) return;
    
    super.update(deltaTime);
    
    // Update game timer
    this.gameTimeLeft -= deltaTime * 1000;
    if (this.gameTimeLeft <= 0) {
      this.gameTimeLeft = 0;
      this.gameOver();
      return;
    }
    
    // Spawn moles
    this.lastMoleSpawn += deltaTime * 1000;
    if (this.lastMoleSpawn >= this.config.moleSpawnRate && this.activeMoles < this.maxActiveMoles) {
      this.spawnMole();
      this.lastMoleSpawn = 0;
    }
    
    // Count active moles
    this.activeMoles = this.moles.filter(mole => mole.state !== 'hidden').length;
  }

  spawnMole() {
    // Find hidden moles
    const hiddenMoles = this.moles.filter(mole => mole.state === 'hidden');
    if (hiddenMoles.length > 0) {
      const randomMole = hiddenMoles[Math.floor(Math.random() * hiddenMoles.length)];
      randomMole.emerge();
    }
  }

  renderUI() {
    super.renderUI();
    
    // Timer
    this.ctx.fillStyle = '#ffffff';
    this.ctx.font = '24px Arial';
    this.ctx.fillText(`Time: ${Math.ceil(this.gameTimeLeft / 1000)}s`, this.canvas.width - 150, 40);
    
    if (!this.gameStarted) {
      // Start screen
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Whack-a-Mole', this.canvas.width / 2, this.canvas.height / 2 - 50);
      
      this.ctx.font = '24px Arial';
      this.ctx.fillText('Click to Start!', this.canvas.width / 2, this.canvas.height / 2 + 20);
      this.ctx.textAlign = 'left';
    }
    
    if (this.gameState === 'gameOver') {
      // Game over screen
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Time\'s Up!', this.canvas.width / 2, this.canvas.height / 2 - 50);
      
      this.ctx.font = '24px Arial';
      this.ctx.fillText(`Final Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2 + 20);
      this.ctx.textAlign = 'left';
    }
  }

  updateParameters(newParams) {
    if (newParams.difficulty) {
      switch (newParams.difficulty) {
        case 'easy':
          this.config.moleSpawnRate = 2000;
          this.maxActiveMoles = 2;
          break;
        case 'medium':
          this.config.moleSpawnRate = 1500;
          this.maxActiveMoles = 3;
          break;
        case 'hard':
          this.config.moleSpawnRate = 1000;
          this.maxActiveMoles = 4;
          break;
      }
    }
    
    Object.assign(this.config, newParams);
  }

  exportGameData() {
    return {
      type: 'whackthemole',
      config: this.config,
      assets: {
        mole: null,
        hammer: null,
        background: null
      }
    };
  }
}
