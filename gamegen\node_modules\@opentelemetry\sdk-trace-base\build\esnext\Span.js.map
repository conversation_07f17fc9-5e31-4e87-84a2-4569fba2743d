{"version": 3, "file": "Span.js", "sourceRoot": "", "sources": ["../../src/Span.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAEL,IAAI,EAUJ,cAAc,GAEf,MAAM,oBAAoB,CAAC;AAC5B,OAAO,EACL,UAAU,EACV,cAAc,EACd,aAAa,EACb,MAAM,EACN,cAAc,EAEd,gBAAgB,EAChB,WAAW,EACX,iBAAiB,EACjB,aAAa,EACb,kBAAkB,GACnB,MAAM,qBAAqB,CAAC;AAE7B,OAAO,EACL,0BAA0B,EAC1B,6BAA6B,EAC7B,uBAAuB,GACxB,MAAM,qCAAqC,CAAC;AAC7C,OAAO,EAAE,kBAAkB,EAAE,MAAM,SAAS,CAAC;AAO7C;;GAEG;AACH,MAAM,OAAO,IAAI;IAgCf;;;;SAIK;IACL,YACE,YAAoB,EACpB,OAAgB,EAChB,QAAgB,EAChB,WAAwB,EACxB,IAAc,EACd,YAAqB,EACrB,QAAgB,EAAE,EAClB,SAAqB,EACrB,gBAA0B,EAAE,mFAAmF;IAC/G,UAA2B;QAzCpB,eAAU,GAAmB,EAAE,CAAC;QAChC,UAAK,GAAW,EAAE,CAAC;QACnB,WAAM,GAAiB,EAAE,CAAC;QAK3B,4BAAuB,GAAG,CAAC,CAAC;QAC5B,wBAAmB,GAAW,CAAC,CAAC;QAChC,uBAAkB,GAAW,CAAC,CAAC;QAGvC,WAAM,GAAe;YACnB,IAAI,EAAE,cAAc,CAAC,KAAK;SAC3B,CAAC;QACF,YAAO,GAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjB,WAAM,GAAG,KAAK,CAAC;QACf,cAAS,GAAW,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QA0BnC,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAC;QACrB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QAEnB,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,qBAAqB,GAAG,aAAa,CAAC,GAAG,EAAE,CAAC;QACjD,IAAI,CAAC,kBAAkB;YACrB,GAAG,GAAG,CAAC,IAAI,CAAC,qBAAqB,GAAG,aAAa,EAAE,CAAC,CAAC;QACvD,IAAI,CAAC,kBAAkB,GAAG,SAAS,IAAI,IAAI,CAAC;QAE5C,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,aAAT,SAAS,cAAT,SAAS,GAAI,GAAG,CAAC,CAAC;QAEjD,IAAI,CAAC,QAAQ,GAAG,YAAY,CAAC,QAAQ,CAAC;QACtC,IAAI,CAAC,sBAAsB,GAAG,YAAY,CAAC,sBAAsB,CAAC;QAClE,IAAI,CAAC,WAAW,GAAG,YAAY,CAAC,aAAa,EAAE,CAAC;QAChD,IAAI,CAAC,0BAA0B;YAC7B,IAAI,CAAC,WAAW,CAAC,yBAAyB,IAAI,CAAC,CAAC;QAElD,IAAI,UAAU,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;SAChC;QAED,IAAI,CAAC,cAAc,GAAG,YAAY,CAAC,sBAAsB,EAAE,CAAC;QAC5D,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAGD,YAAY,CAAC,GAAW,EAAE,KAAc;QACtC,IAAI,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,YAAY,EAAE;YAAE,OAAO,IAAI,CAAC;QACtD,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;YACpB,IAAI,CAAC,IAAI,CAAC,0BAA0B,GAAG,EAAE,CAAC,CAAC;YAC3C,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,wCAAwC,GAAG,EAAE,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC;SACb;QAED,IACE,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,MAAM;YACjC,IAAI,CAAC,WAAW,CAAC,mBAAoB;YACvC,CAAC,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,EAC3D;YACA,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC/B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;QACnD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,aAAa,CAAC,UAA0B;QACtC,KAAK,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;YAC/C,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACzB;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,QAAQ,CACN,IAAY,EACZ,qBAAkD,EAClD,SAAqB;QAErB,IAAI,IAAI,CAAC,YAAY,EAAE;YAAE,OAAO,IAAI,CAAC;QACrC,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,KAAK,CAAC,EAAE;YAC1C,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;YAChC,IAAI,CAAC,mBAAmB,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC;SACb;QACD,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,IAAI,CAAC,WAAW,CAAC,eAAgB,EAAE;YAC3D,IAAI,IAAI,CAAC,mBAAmB,KAAK,CAAC,EAAE;gBAClC,IAAI,CAAC,KAAK,CAAC,wBAAwB,CAAC,CAAC;aACtC;YACD,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC;YACpB,IAAI,CAAC,mBAAmB,EAAE,CAAC;SAC5B;QAED,IAAI,WAAW,CAAC,qBAAqB,CAAC,EAAE;YACtC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;gBAC3B,SAAS,GAAG,qBAAqB,CAAC;aACnC;YACD,qBAAqB,GAAG,SAAS,CAAC;SACnC;QAED,MAAM,UAAU,GAAG,kBAAkB,CAAC,qBAAqB,CAAC,CAAC;QAE7D,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,IAAI;YACJ,UAAU;YACV,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC9B,sBAAsB,EAAE,CAAC;SAC1B,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CAAC,IAAU;QAChB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ,CAAC,KAAa;QACpB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS,CAAC,MAAkB;QAC1B,IAAI,IAAI,CAAC,YAAY,EAAE;YAAE,OAAO,IAAI,CAAC;QACrC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,UAAU,CAAC,IAAY;QACrB,IAAI,IAAI,CAAC,YAAY,EAAE;YAAE,OAAO,IAAI,CAAC;QACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,GAAG,CAAC,OAAmB;QACrB,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACvB,IAAI,CAAC,KAAK,CACR,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,4CAA4C,CAClH,CAAC;YACF,OAAO;SACR;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;QAEnB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QACtC,IAAI,CAAC,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,CACP,qFAAqF,EACrF,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,OAAO,CACb,CAAC;YACF,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAY,CAAC;YAChD,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;SACzB;QAED,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,EAAE;YAChC,IAAI,CAAC,IAAI,CACP,WAAW,IAAI,CAAC,mBAAmB,yCAAyC,CAC7E,CAAC;SACH;QAED,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAEO,QAAQ,CAAC,GAAe;QAC9B,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,GAAG,aAAa,CAAC,GAAG,EAAE,EAAE;YACxD,kCAAkC;YAClC,yCAAyC;YACzC,OAAO,MAAM,CAAC,GAAG,GAAG,IAAI,CAAC,kBAAkB,CAAC,CAAC;SAC9C;QAED,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE;YAC3B,OAAO,cAAc,CAAC,GAAG,CAAC,CAAC;SAC5B;QAED,IAAI,GAAG,YAAY,IAAI,EAAE;YACvB,OAAO,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;SACtC;QAED,IAAI,iBAAiB,CAAC,GAAG,CAAC,EAAE;YAC1B,OAAO,GAAG,CAAC;SACZ;QAED,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAC3B,iDAAiD;YACjD,qDAAqD;YACrD,OAAO,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;SACnC;QAED,MAAM,UAAU,GAAG,aAAa,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACpE,OAAO,UAAU,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC,CAAC;IAChE,CAAC;IAED,WAAW;QACT,OAAO,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC;IAC/B,CAAC;IAED,eAAe,CAAC,SAAoB,EAAE,IAAgB;QACpD,MAAM,UAAU,GAAmB,EAAE,CAAC;QACtC,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE;YACjC,UAAU,CAAC,0BAA0B,CAAC,GAAG,SAAS,CAAC;SACpD;aAAM,IAAI,SAAS,EAAE;YACpB,IAAI,SAAS,CAAC,IAAI,EAAE;gBAClB,UAAU,CAAC,uBAAuB,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;aACjE;iBAAM,IAAI,SAAS,CAAC,IAAI,EAAE;gBACzB,UAAU,CAAC,uBAAuB,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC;aACtD;YACD,IAAI,SAAS,CAAC,OAAO,EAAE;gBACrB,UAAU,CAAC,0BAA0B,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC;aAC5D;YACD,IAAI,SAAS,CAAC,KAAK,EAAE;gBACnB,UAAU,CAAC,6BAA6B,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;aAC7D;SACF;QAED,2CAA2C;QAC3C,IACE,UAAU,CAAC,uBAAuB,CAAC;YACnC,UAAU,CAAC,0BAA0B,CAAC,EACtC;YACA,IAAI,CAAC,QAAQ,CAAC,kBAAkB,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;SACrD;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,iCAAiC,SAAS,EAAE,CAAC,CAAC;SACzD;IACH,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAC;IACxB,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,IAAI,sBAAsB;QACxB,OAAO,IAAI,CAAC,uBAAuB,CAAC;IACtC,CAAC;IAED,IAAI,kBAAkB;QACpB,OAAO,IAAI,CAAC,mBAAmB,CAAC;IAClC,CAAC;IAED,IAAI,iBAAiB;QACnB,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACjC,CAAC;IAEO,YAAY;QAClB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,IAAI,CACP,yDAAyD,IAAI,CAAC,YAAY,CAAC,OAAO,aAAa,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,CAC3H,CAAC;SACH;QACD,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,uDAAuD;IACvD,yDAAyD;IACzD,iDAAiD;IACzC,oBAAoB,CAAC,KAAa,EAAE,KAAa;QACvD,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,EAAE;YACzB,OAAO,KAAK,CAAC;SACd;QACD,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;IAChC,CAAC;IAED;;;;;;;;;;;OAWG;IACK,eAAe,CAAC,KAAyB;QAC/C,MAAM,KAAK,GAAG,IAAI,CAAC,0BAA0B,CAAC;QAC9C,cAAc;QACd,IAAI,KAAK,IAAI,CAAC,EAAE;YACd,kDAAkD;YAClD,IAAI,CAAC,IAAI,CAAC,+CAA+C,KAAK,EAAE,CAAC,CAAC;YAClE,OAAO,KAAK,CAAC;SACd;QAED,SAAS;QACT,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;YAC7B,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SAChD;QAED,mBAAmB;QACnB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACxB,OAAQ,KAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAC7B,OAAO,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CACtE,CAAC;SACH;QAED,mDAAmD;QACnD,OAAO,KAAK,CAAC;IACf,CAAC;CACF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  diag,\n  Exception,\n  HrTime,\n  Link,\n  Span as APISpan,\n  SpanAttributes,\n  SpanAttributeValue,\n  SpanContext,\n  SpanKind,\n  SpanStatus,\n  SpanStatusCode,\n  TimeInput,\n} from '@opentelemetry/api';\nimport {\n  addHrTimes,\n  millisToHrTime,\n  getTimeOrigin,\n  hrTime,\n  hrTimeDuration,\n  InstrumentationLibrary,\n  isAttributeValue,\n  isTimeInput,\n  isTimeInputHrTime,\n  otperformance,\n  sanitizeAttributes,\n} from '@opentelemetry/core';\nimport { IResource } from '@opentelemetry/resources';\nimport {\n  SEMATTRS_EXCEPTION_MESSAGE,\n  SEMATTRS_EXCEPTION_STACKTRACE,\n  SEMATTRS_EXCEPTION_TYPE,\n} from '@opentelemetry/semantic-conventions';\nimport { ExceptionEventName } from './enums';\nimport { ReadableSpan } from './export/ReadableSpan';\nimport { SpanProcessor } from './SpanProcessor';\nimport { TimedEvent } from './TimedEvent';\nimport { Tracer } from './Tracer';\nimport { SpanLimits } from './types';\n\n/**\n * This class represents a span.\n */\nexport class Span implements APISpan, ReadableSpan {\n  // Below properties are included to implement ReadableSpan for export\n  // purposes but are not intended to be written-to directly.\n  private readonly _spanContext: SpanContext;\n  readonly kind: SpanKind;\n  readonly parentSpanId?: string;\n  readonly attributes: SpanAttributes = {};\n  readonly links: Link[] = [];\n  readonly events: TimedEvent[] = [];\n  readonly startTime: HrTime;\n  readonly resource: IResource;\n  readonly instrumentationLibrary: InstrumentationLibrary;\n\n  private _droppedAttributesCount = 0;\n  private _droppedEventsCount: number = 0;\n  private _droppedLinksCount: number = 0;\n\n  name: string;\n  status: SpanStatus = {\n    code: SpanStatusCode.UNSET,\n  };\n  endTime: HrTime = [0, 0];\n  private _ended = false;\n  private _duration: HrTime = [-1, -1];\n  private readonly _spanProcessor: SpanProcessor;\n  private readonly _spanLimits: SpanLimits;\n  private readonly _attributeValueLengthLimit: number;\n\n  private readonly _performanceStartTime: number;\n  private readonly _performanceOffset: number;\n  private readonly _startTimeProvided: boolean;\n\n  /**\n   * Constructs a new Span instance.\n   *\n   * @deprecated calling Span constructor directly is not supported. Please use tracer.startSpan.\n   * */\n  constructor(\n    parentTracer: Tracer,\n    context: Context,\n    spanName: string,\n    spanContext: SpanContext,\n    kind: SpanKind,\n    parentSpanId?: string,\n    links: Link[] = [],\n    startTime?: TimeInput,\n    _deprecatedClock?: unknown, // keeping this argument even though it is unused to ensure backwards compatibility\n    attributes?: SpanAttributes\n  ) {\n    this.name = spanName;\n    this._spanContext = spanContext;\n    this.parentSpanId = parentSpanId;\n    this.kind = kind;\n    this.links = links;\n\n    const now = Date.now();\n    this._performanceStartTime = otperformance.now();\n    this._performanceOffset =\n      now - (this._performanceStartTime + getTimeOrigin());\n    this._startTimeProvided = startTime != null;\n\n    this.startTime = this._getTime(startTime ?? now);\n\n    this.resource = parentTracer.resource;\n    this.instrumentationLibrary = parentTracer.instrumentationLibrary;\n    this._spanLimits = parentTracer.getSpanLimits();\n    this._attributeValueLengthLimit =\n      this._spanLimits.attributeValueLengthLimit || 0;\n\n    if (attributes != null) {\n      this.setAttributes(attributes);\n    }\n\n    this._spanProcessor = parentTracer.getActiveSpanProcessor();\n    this._spanProcessor.onStart(this, context);\n  }\n\n  spanContext(): SpanContext {\n    return this._spanContext;\n  }\n\n  setAttribute(key: string, value?: SpanAttributeValue): this;\n  setAttribute(key: string, value: unknown): this {\n    if (value == null || this._isSpanEnded()) return this;\n    if (key.length === 0) {\n      diag.warn(`Invalid attribute key: ${key}`);\n      return this;\n    }\n    if (!isAttributeValue(value)) {\n      diag.warn(`Invalid attribute value set for key: ${key}`);\n      return this;\n    }\n\n    if (\n      Object.keys(this.attributes).length >=\n        this._spanLimits.attributeCountLimit! &&\n      !Object.prototype.hasOwnProperty.call(this.attributes, key)\n    ) {\n      this._droppedAttributesCount++;\n      return this;\n    }\n    this.attributes[key] = this._truncateToSize(value);\n    return this;\n  }\n\n  setAttributes(attributes: SpanAttributes): this {\n    for (const [k, v] of Object.entries(attributes)) {\n      this.setAttribute(k, v);\n    }\n    return this;\n  }\n\n  /**\n   *\n   * @param name Span Name\n   * @param [attributesOrStartTime] Span attributes or start time\n   *     if type is {@type TimeInput} and 3rd param is undefined\n   * @param [timeStamp] Specified time stamp for the event\n   */\n  addEvent(\n    name: string,\n    attributesOrStartTime?: SpanAttributes | TimeInput,\n    timeStamp?: TimeInput\n  ): this {\n    if (this._isSpanEnded()) return this;\n    if (this._spanLimits.eventCountLimit === 0) {\n      diag.warn('No events allowed.');\n      this._droppedEventsCount++;\n      return this;\n    }\n    if (this.events.length >= this._spanLimits.eventCountLimit!) {\n      if (this._droppedEventsCount === 0) {\n        diag.debug('Dropping extra events.');\n      }\n      this.events.shift();\n      this._droppedEventsCount++;\n    }\n\n    if (isTimeInput(attributesOrStartTime)) {\n      if (!isTimeInput(timeStamp)) {\n        timeStamp = attributesOrStartTime;\n      }\n      attributesOrStartTime = undefined;\n    }\n\n    const attributes = sanitizeAttributes(attributesOrStartTime);\n\n    this.events.push({\n      name,\n      attributes,\n      time: this._getTime(timeStamp),\n      droppedAttributesCount: 0,\n    });\n    return this;\n  }\n\n  addLink(link: Link): this {\n    this.links.push(link);\n    return this;\n  }\n\n  addLinks(links: Link[]): this {\n    this.links.push(...links);\n    return this;\n  }\n\n  setStatus(status: SpanStatus): this {\n    if (this._isSpanEnded()) return this;\n    this.status = status;\n    return this;\n  }\n\n  updateName(name: string): this {\n    if (this._isSpanEnded()) return this;\n    this.name = name;\n    return this;\n  }\n\n  end(endTime?: TimeInput): void {\n    if (this._isSpanEnded()) {\n      diag.error(\n        `${this.name} ${this._spanContext.traceId}-${this._spanContext.spanId} - You can only call end() on a span once.`\n      );\n      return;\n    }\n    this._ended = true;\n\n    this.endTime = this._getTime(endTime);\n    this._duration = hrTimeDuration(this.startTime, this.endTime);\n\n    if (this._duration[0] < 0) {\n      diag.warn(\n        'Inconsistent start and end time, startTime > endTime. Setting span duration to 0ms.',\n        this.startTime,\n        this.endTime\n      );\n      this.endTime = this.startTime.slice() as HrTime;\n      this._duration = [0, 0];\n    }\n\n    if (this._droppedEventsCount > 0) {\n      diag.warn(\n        `Dropped ${this._droppedEventsCount} events because eventCountLimit reached`\n      );\n    }\n\n    this._spanProcessor.onEnd(this);\n  }\n\n  private _getTime(inp?: TimeInput): HrTime {\n    if (typeof inp === 'number' && inp < otperformance.now()) {\n      // must be a performance timestamp\n      // apply correction and convert to hrtime\n      return hrTime(inp + this._performanceOffset);\n    }\n\n    if (typeof inp === 'number') {\n      return millisToHrTime(inp);\n    }\n\n    if (inp instanceof Date) {\n      return millisToHrTime(inp.getTime());\n    }\n\n    if (isTimeInputHrTime(inp)) {\n      return inp;\n    }\n\n    if (this._startTimeProvided) {\n      // if user provided a time for the start manually\n      // we can't use duration to calculate event/end times\n      return millisToHrTime(Date.now());\n    }\n\n    const msDuration = otperformance.now() - this._performanceStartTime;\n    return addHrTimes(this.startTime, millisToHrTime(msDuration));\n  }\n\n  isRecording(): boolean {\n    return this._ended === false;\n  }\n\n  recordException(exception: Exception, time?: TimeInput): void {\n    const attributes: SpanAttributes = {};\n    if (typeof exception === 'string') {\n      attributes[SEMATTRS_EXCEPTION_MESSAGE] = exception;\n    } else if (exception) {\n      if (exception.code) {\n        attributes[SEMATTRS_EXCEPTION_TYPE] = exception.code.toString();\n      } else if (exception.name) {\n        attributes[SEMATTRS_EXCEPTION_TYPE] = exception.name;\n      }\n      if (exception.message) {\n        attributes[SEMATTRS_EXCEPTION_MESSAGE] = exception.message;\n      }\n      if (exception.stack) {\n        attributes[SEMATTRS_EXCEPTION_STACKTRACE] = exception.stack;\n      }\n    }\n\n    // these are minimum requirements from spec\n    if (\n      attributes[SEMATTRS_EXCEPTION_TYPE] ||\n      attributes[SEMATTRS_EXCEPTION_MESSAGE]\n    ) {\n      this.addEvent(ExceptionEventName, attributes, time);\n    } else {\n      diag.warn(`Failed to record an exception ${exception}`);\n    }\n  }\n\n  get duration(): HrTime {\n    return this._duration;\n  }\n\n  get ended(): boolean {\n    return this._ended;\n  }\n\n  get droppedAttributesCount(): number {\n    return this._droppedAttributesCount;\n  }\n\n  get droppedEventsCount(): number {\n    return this._droppedEventsCount;\n  }\n\n  get droppedLinksCount(): number {\n    return this._droppedLinksCount;\n  }\n\n  private _isSpanEnded(): boolean {\n    if (this._ended) {\n      diag.warn(\n        `Can not execute the operation on ended Span {traceId: ${this._spanContext.traceId}, spanId: ${this._spanContext.spanId}}`\n      );\n    }\n    return this._ended;\n  }\n\n  // Utility function to truncate given value within size\n  // for value type of string, will truncate to given limit\n  // for type of non-string, will return same value\n  private _truncateToLimitUtil(value: string, limit: number): string {\n    if (value.length <= limit) {\n      return value;\n    }\n    return value.substr(0, limit);\n  }\n\n  /**\n   * If the given attribute value is of type string and has more characters than given {@code attributeValueLengthLimit} then\n   * return string with truncated to {@code attributeValueLengthLimit} characters\n   *\n   * If the given attribute value is array of strings then\n   * return new array of strings with each element truncated to {@code attributeValueLengthLimit} characters\n   *\n   * Otherwise return same Attribute {@code value}\n   *\n   * @param value Attribute value\n   * @returns truncated attribute value if required, otherwise same value\n   */\n  private _truncateToSize(value: SpanAttributeValue): SpanAttributeValue {\n    const limit = this._attributeValueLengthLimit;\n    // Check limit\n    if (limit <= 0) {\n      // Negative values are invalid, so do not truncate\n      diag.warn(`Attribute value limit must be positive, got ${limit}`);\n      return value;\n    }\n\n    // String\n    if (typeof value === 'string') {\n      return this._truncateToLimitUtil(value, limit);\n    }\n\n    // Array of strings\n    if (Array.isArray(value)) {\n      return (value as []).map(val =>\n        typeof val === 'string' ? this._truncateToLimitUtil(val, limit) : val\n      );\n    }\n\n    // Other types, no need to apply value length limit\n    return value;\n  }\n}\n"]}