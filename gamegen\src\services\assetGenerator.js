import geminiService from './geminiService.js';

class AssetGenerator {
  constructor() {
    this.generatedAssets = new Map();
    this.assetCache = new Map();
    this.stylePresets = {
      'pixel-art': {
        description: 'Retro pixel art style with 8-bit aesthetics, blocky shapes, limited color palette',
        colors: ['#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF'],
        resolution: '32x32'
      },
      'cartoon': {
        description: 'Colorful cartoon style with bold outlines, vibrant colors, and friendly appearance',
        colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD'],
        resolution: '64x64'
      },
      'realistic': {
        description: 'Realistic style with detailed textures, natural lighting, and photographic quality',
        colors: ['#8B4513', '#228B22', '#4169E1', '#DC143C', '#FFD700', '#9370DB'],
        resolution: '128x128'
      },
      'minimalist': {
        description: 'Clean minimalist style with simple shapes, flat colors, and geometric design',
        colors: ['#2C3E50', '#E74C3C', '#3498DB', '#2ECC71', '#F39C12', '#9B59B6'],
        resolution: '48x48'
      },
      'neon': {
        description: 'Futuristic neon style with glowing effects, dark backgrounds, and electric colors',
        colors: ['#FF0080', '#00FF80', '#8000FF', '#FF8000', '#0080FF', '#80FF00'],
        resolution: '64x64'
      }
    };
  }

  async generateAssetPack(gameType, style, customPrompt = '') {
    try {
      const assetPack = {
        gameType,
        style,
        timestamp: Date.now(),
        assets: {}
      };

      // Generate different types of assets based on game type
      const assetTypes = this.getAssetTypesForGame(gameType);
      
      for (const assetType of assetTypes) {
        console.log(`Generating ${assetType} for ${gameType}...`);
        const asset = await this.generateSingleAsset(gameType, assetType, style, customPrompt);
        assetPack.assets[assetType] = asset;
      }

      // Cache the generated pack
      const packId = `${gameType}_${style}_${Date.now()}`;
      this.assetCache.set(packId, assetPack);

      return {
        packId,
        assetPack,
        success: true
      };
    } catch (error) {
      console.error('Error generating asset pack:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  getAssetTypesForGame(gameType) {
    const assetMap = {
      'flappybird': ['character', 'background', 'obstacles', 'ui'],
      'speedrunner': ['character', 'background', 'platforms', 'collectibles', 'ui'],
      'whackthemole': ['character', 'background', 'holes', 'hammer', 'ui'],
      'match3': ['gems', 'background', 'effects', 'ui'],
      'crossyroad': ['character', 'background', 'vehicles', 'obstacles', 'ui']
    };
    
    return assetMap[gameType] || ['character', 'background', 'ui'];
  }

  async generateSingleAsset(gameType, assetType, style, customPrompt) {
    const stylePreset = this.stylePresets[style] || this.stylePresets['cartoon'];
    
    // Build comprehensive prompt
    const prompt = this.buildAssetPrompt(gameType, assetType, stylePreset, customPrompt);
    
    try {
      // Use Gemini to generate detailed asset description
      const assetDescription = await geminiService.generateGameAssets(prompt, assetType, gameType);
      
      // Generate placeholder asset (in a real implementation, this would call an image generation API)
      const placeholderAsset = this.generatePlaceholderAsset(assetType, stylePreset, assetDescription);
      
      return {
        type: assetType,
        style: style,
        description: assetDescription.description,
        specifications: this.extractAssetSpecs(assetDescription.description, stylePreset),
        placeholder: placeholderAsset,
        // In a real implementation, these would be actual generated image URLs
        urls: {
          small: `/assets/generated/${gameType}/${assetType}_small.png`,
          medium: `/assets/generated/${gameType}/${assetType}_medium.png`,
          large: `/assets/generated/${gameType}/${assetType}_large.png`
        },
        metadata: {
          gameType,
          assetType,
          style,
          timestamp: Date.now(),
          resolution: stylePreset.resolution
        }
      };
    } catch (error) {
      console.error(`Error generating ${assetType}:`, error);
      return this.getFallbackAsset(assetType, style);
    }
  }

  buildAssetPrompt(gameType, assetType, stylePreset, customPrompt) {
    const basePrompts = {
      'flappybird': {
        'character': `Design a bird character for a Flappy Bird game in ${stylePreset.description}. The bird should be cute and appealing, with clear wing details for animation. Colors should be bright and cheerful.`,
        'background': `Create a scrolling background for a Flappy Bird game featuring a sky scene with clouds, possibly some distant mountains or buildings. Style: ${stylePreset.description}.`,
        'obstacles': `Design pipe obstacles for a Flappy Bird game. They should be green pipes with caps, clearly defined collision boundaries. Style: ${stylePreset.description}.`,
        'ui': `Design UI elements including score display, game over screen, and start button for a Flappy Bird game. Style: ${stylePreset.description}.`
      },
      'speedrunner': {
        'character': `Design a runner character for a platformer game. The character should look athletic and dynamic, suitable for running and jumping. Style: ${stylePreset.description}.`,
        'background': `Create a side-scrolling background for a speed runner game with multiple layers for parallax effect. Include sky, distant mountains, and ground elements. Style: ${stylePreset.description}.`,
        'platforms': `Design various platform types for a speed runner game including regular platforms, moving platforms, and breakable platforms. Style: ${stylePreset.description}.`,
        'collectibles': `Design collectible items like coins, gems, or power-ups for a speed runner game. They should be visually appealing and clearly distinguishable. Style: ${stylePreset.description}.`
      },
      'whackthemole': {
        'character': `Design cute mole characters for a Whack-a-Mole game. Include different expressions (normal, surprised, hit). Style: ${stylePreset.description}.`,
        'background': `Create a grassy field background for a Whack-a-Mole game with holes for moles to emerge from. Style: ${stylePreset.description}.`,
        'holes': `Design mole holes with dark interiors and grass around the edges. Style: ${stylePreset.description}.`,
        'hammer': `Design a cartoon hammer for whacking moles. Should look fun and not too violent. Style: ${stylePreset.description}.`
      },
      'match3': {
        'gems': `Design a set of 6 different gem types for a Match-3 game. Each should be distinct in color and shape but maintain visual consistency. Style: ${stylePreset.description}.`,
        'background': `Create a mystical or magical background for a Match-3 puzzle game. Should not be too busy to distract from gameplay. Style: ${stylePreset.description}.`,
        'effects': `Design particle effects for gem matches, explosions, and special moves in a Match-3 game. Style: ${stylePreset.description}.`
      },
      'crossyroad': {
        'character': `Design a small character for a Crossy Road style game. Should be cute and easily recognizable at small sizes. Style: ${stylePreset.description}.`,
        'background': `Create lane backgrounds for a Crossy Road game including grass (safe), road (with markings), and water sections. Style: ${stylePreset.description}.`,
        'vehicles': `Design various vehicles (cars, trucks, buses) for a Crossy Road game. Different colors and sizes. Style: ${stylePreset.description}.`,
        'obstacles': `Design logs for water sections and other obstacles for a Crossy Road game. Style: ${stylePreset.description}.`
      }
    };

    let prompt = basePrompts[gameType]?.[assetType] || `Design ${assetType} assets for a ${gameType} game in ${stylePreset.description}.`;
    
    if (customPrompt) {
      prompt += ` Additional requirements: ${customPrompt}`;
    }

    prompt += ` Use colors from this palette: ${stylePreset.colors.join(', ')}. Target resolution: ${stylePreset.resolution}.`;

    return prompt;
  }

  extractAssetSpecs(description, stylePreset) {
    // Extract specifications from the AI-generated description
    return {
      style: stylePreset.description,
      colors: stylePreset.colors,
      resolution: stylePreset.resolution,
      animationFrames: this.extractAnimationInfo(description),
      dimensions: this.extractDimensions(description),
      features: this.extractFeatures(description)
    };
  }

  extractAnimationInfo(description) {
    // Simple extraction of animation-related keywords
    const animationKeywords = ['animation', 'frames', 'movement', 'rotation', 'bounce'];
    const hasAnimation = animationKeywords.some(keyword => 
      description.toLowerCase().includes(keyword)
    );
    return hasAnimation ? 4 : 1; // Default to 4 frames if animation mentioned
  }

  extractDimensions(description) {
    // Extract dimension information from description
    const dimensionMatch = description.match(/(\d+)x(\d+)/);
    if (dimensionMatch) {
      return {
        width: parseInt(dimensionMatch[1]),
        height: parseInt(dimensionMatch[2])
      };
    }
    return { width: 64, height: 64 }; // Default dimensions
  }

  extractFeatures(description) {
    // Extract key features mentioned in the description
    const features = [];
    const featureKeywords = [
      'colorful', 'animated', 'glowing', 'transparent', 'textured',
      'shiny', 'metallic', 'wooden', 'stone', 'magical'
    ];
    
    featureKeywords.forEach(keyword => {
      if (description.toLowerCase().includes(keyword)) {
        features.push(keyword);
      }
    });
    
    return features;
  }

  generatePlaceholderAsset(assetType, stylePreset, assetDescription) {
    // Generate a simple placeholder representation
    const canvas = document.createElement('canvas');
    canvas.width = 64;
    canvas.height = 64;
    const ctx = canvas.getContext('2d');

    // Use the first color from the style preset
    const primaryColor = stylePreset.colors[0];
    
    // Draw a simple shape based on asset type
    ctx.fillStyle = primaryColor;
    
    switch (assetType) {
      case 'character':
        // Draw a simple character shape
        ctx.fillRect(20, 10, 24, 40);
        ctx.fillRect(24, 6, 16, 16); // head
        break;
      case 'background':
        // Draw a gradient background
        const gradient = ctx.createLinearGradient(0, 0, 0, 64);
        gradient.addColorStop(0, stylePreset.colors[1]);
        gradient.addColorStop(1, stylePreset.colors[2]);
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 64, 64);
        break;
      case 'obstacles':
      case 'platforms':
        // Draw rectangular obstacles
        ctx.fillRect(8, 24, 48, 16);
        break;
      case 'collectibles':
      case 'gems':
        // Draw circular collectibles
        ctx.beginPath();
        ctx.arc(32, 32, 16, 0, 2 * Math.PI);
        ctx.fill();
        break;
      default:
        // Default square
        ctx.fillRect(16, 16, 32, 32);
    }

    return canvas.toDataURL();
  }

  getFallbackAsset(assetType, style) {
    return {
      type: assetType,
      style: style,
      description: `Fallback ${assetType} asset in ${style} style`,
      specifications: {
        style: style,
        colors: ['#888888'],
        resolution: '32x32',
        animationFrames: 1,
        dimensions: { width: 32, height: 32 },
        features: ['fallback']
      },
      placeholder: this.generatePlaceholderAsset(assetType, { colors: ['#888888'] }, {}),
      urls: {
        small: `/assets/fallback/${assetType}_small.png`,
        medium: `/assets/fallback/${assetType}_medium.png`,
        large: `/assets/fallback/${assetType}_large.png`
      },
      metadata: {
        assetType,
        style,
        timestamp: Date.now(),
        resolution: '32x32',
        fallback: true
      }
    };
  }

  getAssetPack(packId) {
    return this.assetCache.get(packId);
  }

  getAllAssetPacks() {
    return Array.from(this.assetCache.entries()).map(([id, pack]) => ({
      id,
      gameType: pack.gameType,
      style: pack.style,
      timestamp: pack.timestamp
    }));
  }

  clearCache() {
    this.assetCache.clear();
  }

  // Method to simulate real image generation API integration
  async integrateWithImageAPI(prompt, style, dimensions) {
    // This would integrate with actual image generation APIs like:
    // - DALL-E 3
    // - Midjourney
    // - Stable Diffusion
    // - Adobe Firefly
    
    console.log('Would call image generation API with:', {
      prompt,
      style,
      dimensions
    });
    
    // For now, return a placeholder
    return {
      success: true,
      imageUrl: '/api/placeholder/generated-image.png',
      metadata: {
        prompt,
        style,
        dimensions,
        generatedAt: Date.now()
      }
    };
  }
}

export default new AssetGenerator();
