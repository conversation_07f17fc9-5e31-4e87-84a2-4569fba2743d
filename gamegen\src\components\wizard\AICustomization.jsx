import { useState, useEffect } from 'react';
import { Spark<PERSON>, Image, Palette, Music, Wand2 } from 'lucide-react';
import assetGenerator from '../../services/assetGenerator';
import audioManager from '../../services/audioManager';

const stylePresets = [
  { id: 'pixel-art', name: 'Pixel Art', description: 'Retro 8-bit style', icon: '🎮' },
  { id: 'cartoon', name: 'Cartoon', description: 'Colorful and friendly', icon: '🎨' },
  { id: 'realistic', name: 'Realistic', description: 'Detailed and lifelike', icon: '📸' },
  { id: 'minimalist', name: 'Minimalist', description: 'Clean and simple', icon: '⚪' },
  { id: 'neon', name: 'Neon', description: 'Futuristic glow effects', icon: '🌟' }
];

const musicStyles = [
  { id: 'upbeat', name: 'Upbeat', description: 'Energetic and motivating' },
  { id: 'ambient', name: 'Ambient', description: 'Calm and atmospheric' },
  { id: 'retro', name: '<PERSON><PERSON>', description: '8-bit chiptune style' },
  { id: 'epic', name: 'Epic', description: 'Dramatic orchestral' },
  { id: 'minimal', name: 'Minimal', description: 'Simple and clean' }
];

function AICustomization({ gameData, onComplete, loading }) {
  const [selectedStyle, setSelectedStyle] = useState('cartoon');
  const [selectedMusicStyle, setSelectedMusicStyle] = useState('upbeat');
  const [customPrompts, setCustomPrompts] = useState({
    visual: '',
    audio: ''
  });
  const [generationStatus, setGenerationStatus] = useState({
    assets: 'idle', // idle, generating, complete, error
    audio: 'idle'
  });
  const [generatedAssets, setGeneratedAssets] = useState(null);
  const [generatedAudio, setGeneratedAudio] = useState(null);
  const [previewAsset, setPreviewAsset] = useState(null);

  const handleStyleSelect = (styleId) => {
    setSelectedStyle(styleId);
    setGeneratedAssets(null); // Reset generated assets when style changes
  };

  const handleMusicStyleSelect = (styleId) => {
    setSelectedMusicStyle(styleId);
    setGeneratedAudio(null); // Reset generated audio when style changes
  };

  const handlePromptChange = (type, value) => {
    setCustomPrompts(prev => ({
      ...prev,
      [type]: value
    }));
  };

  const generateAssets = async () => {
    if (!gameData.template) return;

    setGenerationStatus(prev => ({ ...prev, assets: 'generating' }));

    try {
      const result = await assetGenerator.generateAssetPack(
        gameData.template.id,
        selectedStyle,
        customPrompts.visual
      );

      if (result.success) {
        setGeneratedAssets(result.assetPack);
        setGenerationStatus(prev => ({ ...prev, assets: 'complete' }));

        // Set first asset as preview
        const firstAssetType = Object.keys(result.assetPack.assets)[0];
        if (firstAssetType) {
          setPreviewAsset(result.assetPack.assets[firstAssetType]);
        }
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Asset generation failed:', error);
      setGenerationStatus(prev => ({ ...prev, assets: 'error' }));
    }
  };

  const generateAudio = async () => {
    if (!gameData.template) return;

    setGenerationStatus(prev => ({ ...prev, audio: 'generating' }));

    try {
      const result = await audioManager.generateGameAudio(
        gameData.template.id,
        selectedMusicStyle,
        customPrompts.audio
      );

      if (result.success) {
        setGeneratedAudio(result.audioPackage);
        setGenerationStatus(prev => ({ ...prev, audio: 'complete' }));
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Audio generation failed:', error);
      setGenerationStatus(prev => ({ ...prev, audio: 'error' }));
    }
  };

  const handleContinue = () => {
    onComplete({
      assets: generatedAssets,
      audio: generatedAudio,
      customizations: {
        visualStyle: selectedStyle,
        musicStyle: selectedMusicStyle,
        prompts: customPrompts
      }
    });
  };

  const canContinue = generationStatus.assets === 'complete' && generationStatus.audio === 'complete';

  return (
    <div className="ai-customization">
      <div className="step-header">
        <h2>AI-Powered Customization</h2>
        <p>Let AI generate unique assets and audio for your {gameData.template?.name} game.</p>
      </div>



      <div className="customization-sections">
        {/* Visual Style Section */}
        <div className="customization-section">
          <div className="section-header">
            <Image className="section-icon" />
            <h3>Visual Style</h3>
          </div>

          <div className="style-grid">
            {stylePresets.map((style) => (
              <div
                key={style.id}
                className={`style-card ${selectedStyle === style.id ? 'selected' : ''}`}
                onClick={() => handleStyleSelect(style.id)}
              >
                <div className="style-icon">{style.icon}</div>
                <h4>{style.name}</h4>
                <p>{style.description}</p>
              </div>
            ))}
          </div>

          <div className="custom-prompt">
            <label htmlFor="visual-prompt">Custom Visual Prompt (Optional)</label>
            <textarea
              id="visual-prompt"
              name="visual-prompt"
              placeholder="Describe any specific visual elements you want (e.g., 'make the character a robot', 'use purple and gold colors')"
              value={customPrompts.visual || ''}
              onChange={(e) => handlePromptChange('visual', e.target.value)}
              rows={3}
              autoComplete="off"
              spellCheck="true"
            />
          </div>

          <button
            className="generate-button"
            onClick={generateAssets}
            disabled={loading || generationStatus.assets === 'generating'}
          >
            <Wand2 className="button-icon" />
            {generationStatus.assets === 'generating' ? 'Generating Assets...' : 'Generate Visual Assets'}
          </button>

          {generationStatus.assets === 'complete' && generatedAssets && (
            <div className="generation-result">
              <div className="result-header">
                <Sparkles className="result-icon" />
                <span>Assets Generated Successfully!</span>
              </div>
              <div className="asset-preview">
                {Object.entries(generatedAssets.assets).map(([type, asset]) => (
                  <div key={type} className="asset-item">
                    <div className="asset-thumbnail">
                      {asset.placeholder && (
                        <img src={asset.placeholder} alt={`${type} preview`} />
                      )}
                    </div>
                    <span className="asset-type">{type}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {generationStatus.assets === 'error' && (
            <div className="generation-error">
              <p>Failed to generate assets. Please try again.</p>
              <button onClick={generateAssets} className="retry-button">Retry</button>
            </div>
          )}
        </div>

        {/* Audio Style Section */}
        <div className="customization-section">
          <div className="section-header">
            <Music className="section-icon" />
            <h3>Audio Style</h3>
          </div>

          <div className="music-grid">
            {musicStyles.map((style) => (
              <div
                key={style.id}
                className={`music-card ${selectedMusicStyle === style.id ? 'selected' : ''}`}
                onClick={() => handleMusicStyleSelect(style.id)}
              >
                <h4>{style.name}</h4>
                <p>{style.description}</p>
              </div>
            ))}
          </div>

          <div className="custom-prompt">
            <label htmlFor="audio-prompt">Custom Audio Prompt (Optional)</label>
            <textarea
              id="audio-prompt"
              name="audio-prompt"
              placeholder="Describe the mood or style you want for the music and sound effects"
              value={customPrompts.audio || ''}
              onChange={(e) => handlePromptChange('audio', e.target.value)}
              rows={3}
              autoComplete="off"
              spellCheck="true"
            />
          </div>

          <button
            className="generate-button"
            onClick={generateAudio}
            disabled={loading || generationStatus.audio === 'generating'}
          >
            <Wand2 className="button-icon" />
            {generationStatus.audio === 'generating' ? 'Generating Audio...' : 'Generate Audio'}
          </button>

          {generationStatus.audio === 'complete' && generatedAudio && (
            <div className="generation-result">
              <div className="result-header">
                <Sparkles className="result-icon" />
                <span>Audio Generated Successfully!</span>
              </div>
              <div className="audio-preview">
                <div className="audio-item">
                  <span>Background Music</span>
                  <span className="audio-duration">
                    {generatedAudio.music?.metadata?.duration || 30}s loop
                  </span>
                </div>
                <div className="audio-item">
                  <span>Sound Effects</span>
                  <span className="audio-count">
                    {Object.keys(generatedAudio.soundEffects || {}).length} effects
                  </span>
                </div>
              </div>
            </div>
          )}

          {generationStatus.audio === 'error' && (
            <div className="generation-error">
              <p>Failed to generate audio. Please try again.</p>
              <button onClick={generateAudio} className="retry-button">Retry</button>
            </div>
          )}
        </div>
      </div>

      <div className="step-actions">
        <button
          className="continue-button"
          onClick={handleContinue}
          disabled={!canContinue || loading}
        >
          Continue to Parameter Tuning
        </button>

        {!canContinue && (
          <p className="continue-hint">
            Generate both visual assets and audio to continue
          </p>
        )}
      </div>
    </div>
  );
}

export default AICustomization;
