import { GoogleGenerativeAI } from '@google/generative-ai';

class GeminiService {
  constructor() {
    this.apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    if (!this.apiKey) {
      console.warn('Gemini API key not found. Please set VITE_GEMINI_API_KEY in your .env file');
    }
    this.genAI = this.apiKey ? new GoogleGenerativeAI(this.apiKey) : null;
  }

  async generateGameAssets(prompt, assetType, gameTemplate) {
    if (!this.genAI) {
      throw new Error('Gemini API not configured');
    }

    try {
      const model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const enhancedPrompt = this.buildAssetPrompt(prompt, assetType, gameTemplate);
      const result = await model.generateContent(enhancedPrompt);
      const response = await result.response;
      
      return this.parseAssetResponse(response.text(), assetType);
    } catch (error) {
      console.error('Error generating assets:', error);
      throw new Error(`Failed to generate ${assetType}: ${error.message}`);
    }
  }

  async tuneGameParameters(userRequest, currentParams, gameTemplate) {
    if (!this.genAI) {
      throw new Error('Gemini API not configured');
    }

    try {
      const model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const prompt = this.buildParameterTuningPrompt(userRequest, currentParams, gameTemplate);
      const result = await model.generateContent(prompt);
      const response = await result.response;
      
      return this.parseParameterResponse(response.text(), currentParams);
    } catch (error) {
      console.error('Error tuning parameters:', error);
      throw new Error(`Failed to tune parameters: ${error.message}`);
    }
  }

  buildAssetPrompt(userPrompt, assetType, gameTemplate) {
    const basePrompts = {
      character: `Create a detailed description for a ${gameTemplate} game character sprite. User request: "${userPrompt}". 
                 Provide specific details about appearance, colors, style, and animations needed.`,
      background: `Design a background environment for a ${gameTemplate} game. User request: "${userPrompt}". 
                  Describe the setting, atmosphere, colors, and visual elements.`,
      objects: `Design game objects and items for a ${gameTemplate} game. User request: "${userPrompt}". 
               Describe obstacles, collectibles, power-ups, and interactive elements.`,
      ui: `Design UI elements for a ${gameTemplate} game interface. User request: "${userPrompt}". 
          Describe buttons, menus, score displays, and interface components.`
    };

    return basePrompts[assetType] || userPrompt;
  }

  buildParameterTuningPrompt(userRequest, currentParams, gameTemplate) {
    return `You are a game designer helping to tune parameters for a ${gameTemplate} game.
    
Current game parameters:
${JSON.stringify(currentParams, null, 2)}

User request: "${userRequest}"

Please provide updated parameters in JSON format that address the user's request. 
Only modify parameters that are relevant to the request. Keep the same structure.
Focus on gameplay elements like speed, difficulty, spawn rates, physics, and scoring.

Respond with only the JSON object of updated parameters.`;
  }

  parseAssetResponse(response, assetType) {
    // For now, return the text description
    // In a full implementation, this would integrate with image generation APIs
    return {
      type: assetType,
      description: response,
      // Placeholder for actual asset URLs that would be generated
      assets: {
        preview: '/api/placeholder/preview.png',
        sprite: '/api/placeholder/sprite.png',
        animation: '/api/placeholder/animation.gif'
      }
    };
  }

  parseParameterResponse(response, currentParams) {
    try {
      // Extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const updatedParams = JSON.parse(jsonMatch[0]);
        return { ...currentParams, ...updatedParams };
      }
      return currentParams;
    } catch (error) {
      console.error('Error parsing parameter response:', error);
      return currentParams;
    }
  }

  async generateAudioDescription(prompt, audioType, gameTemplate) {
    if (!this.genAI) {
      throw new Error('Gemini API not configured');
    }

    try {
      const model = this.genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const enhancedPrompt = `Create audio specifications for a ${gameTemplate} game.
      Type: ${audioType}
      User request: "${prompt}"
      
      Provide detailed specifications for ${audioType === 'music' ? 'background music' : 'sound effects'} including:
      - Mood and atmosphere
      - Tempo and rhythm (for music)
      - Instruments or sound sources
      - Duration and loop points
      - Volume and mixing suggestions`;

      const result = await model.generateContent(enhancedPrompt);
      const response = await result.response;
      
      return {
        type: audioType,
        description: response.text(),
        specifications: this.parseAudioSpecs(response.text())
      };
    } catch (error) {
      console.error('Error generating audio description:', error);
      throw new Error(`Failed to generate audio description: ${error.message}`);
    }
  }

  parseAudioSpecs(description) {
    // Extract key specifications from the description
    return {
      mood: 'energetic', // Default values, would be parsed from description
      tempo: 'medium',
      duration: 30,
      loop: true
    };
  }
}

export default new GeminiService();
