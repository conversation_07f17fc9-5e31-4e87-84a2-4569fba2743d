import { GameEngine } from '../../services/gameEngine.js';
import { GameObject, Player, Obstacle } from '../../services/GameObject.js';

// Flappy Bird specific classes
class Bird extends Player {
  constructor(x, y) {
    super(x, y, 40, 30);
    this.jumpForce = -400;
    this.physics.gravity = true;
    this.color = '#FFD700';
    this.rotation = 0;
  }

  update(deltaTime) {
    super.update(deltaTime);
    
    // Rotate bird based on velocity
    this.rotation = Math.min(Math.max(this.velocity.y * 0.002, -0.5), 0.5);
    
    // Prevent bird from going off screen
    if (this.y < 0) {
      this.y = 0;
      this.velocity.y = 0;
    }
  }

  render(ctx) {
    ctx.save();
    ctx.translate(this.x + this.width/2, this.y + this.height/2);
    ctx.rotate(this.rotation);
    ctx.fillStyle = this.color;
    ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
    
    // Draw simple bird shape
    ctx.fillStyle = '#FFA500';
    ctx.fillRect(-this.width/2 + 5, -this.height/2 + 5, 10, 8); // beak
    ctx.fillStyle = '#000';
    ctx.fillRect(-this.width/2 + 8, -this.height/2 + 8, 3, 3); // eye
    
    ctx.restore();
  }

  jump() {
    this.velocity.y = this.jumpForce;
  }

  onCollision(other) {
    if (other.type === 'pipe' || other.type === 'ground') {
      this.takeDamage(100); // Instant death
    }
  }
}

class Pipe extends Obstacle {
  constructor(x, y, width, height, isTop = false) {
    super(x, y, width, height);
    this.type = 'pipe';
    this.isTop = isTop;
    this.color = '#228B22';
    this.passed = false;
  }

  render(ctx) {
    ctx.fillStyle = this.color;
    ctx.fillRect(this.x, this.y, this.width, this.height);
    
    // Draw pipe cap
    const capHeight = 30;
    const capWidth = this.width + 10;
    ctx.fillRect(this.x - 5, this.isTop ? this.y + this.height - capHeight : this.y, capWidth, capHeight);
    
    // Draw pipe details
    ctx.strokeStyle = '#006400';
    ctx.lineWidth = 2;
    ctx.strokeRect(this.x, this.y, this.width, this.height);
  }
}

class Ground extends Obstacle {
  constructor(x, y, width, height) {
    super(x, y, width, height);
    this.type = 'ground';
    this.color = '#8B4513';
  }

  render(ctx) {
    ctx.fillStyle = this.color;
    ctx.fillRect(this.x, this.y, this.width, this.height);
    
    // Draw grass on top
    ctx.fillStyle = '#228B22';
    ctx.fillRect(this.x, this.y, this.width, 10);
  }
}

export class FlappyBirdGame extends GameEngine {
  constructor(canvas, config = {}) {
    const defaultConfig = {
      width: 800,
      height: 600,
      background: { color: '#87CEEB' },
      difficulty: 'medium',
      pipeGap: 150,
      pipeSpeed: 200,
      pipeSpawnRate: 2000, // milliseconds
      ...config
    };
    
    super(canvas, defaultConfig);
    
    this.bird = null;
    this.pipes = [];
    this.ground = null;
    this.lastPipeSpawn = 0;
    this.gameStarted = false;
    
    this.setupGame();
  }

  setupGame() {
    // Create bird
    this.bird = new Bird(100, this.canvas.height / 2);
    this.addGameObject(this.bird);
    
    // Create ground
    this.ground = new Ground(0, this.canvas.height - 50, this.canvas.width, 50);
    this.addGameObject(this.ground);
    
    // Bind controls
    this.setupControls();
  }

  setupControls() {
    const handleJump = () => {
      if (!this.gameStarted) {
        this.gameStarted = true;
        this.start();
      }
      if (this.gameState === 'playing') {
        this.bird.jump();
      }
    };

    // Keyboard controls
    window.addEventListener('keydown', (e) => {
      if (e.code === 'Space' || e.code === 'ArrowUp') {
        e.preventDefault();
        handleJump();
      }
    });

    // Mouse/Touch controls
    this.canvas.addEventListener('click', handleJump);
    this.canvas.addEventListener('touchstart', (e) => {
      e.preventDefault();
      handleJump();
    });
  }

  update(deltaTime) {
    if (!this.gameStarted) return;
    
    super.update(deltaTime);
    
    // Spawn pipes
    this.lastPipeSpawn += deltaTime * 1000;
    if (this.lastPipeSpawn >= this.config.pipeSpawnRate) {
      this.spawnPipe();
      this.lastPipeSpawn = 0;
    }
    
    // Move pipes
    this.pipes.forEach(pipe => {
      pipe.x -= this.config.pipeSpeed * deltaTime;
      
      // Check if bird passed pipe (for scoring)
      if (!pipe.passed && pipe.x + pipe.width < this.bird.x) {
        pipe.passed = true;
        if (!pipe.isTop) { // Only count bottom pipes
          this.updateScore(1);
        }
      }
    });
    
    // Remove off-screen pipes
    this.pipes = this.pipes.filter(pipe => {
      if (pipe.x + pipe.width < 0) {
        this.removeGameObject(pipe);
        return false;
      }
      return true;
    });
    
    // Check if bird hit ground or went too high
    if (this.bird.y + this.bird.height >= this.ground.y || this.bird.y < 0) {
      this.gameOver();
    }
  }

  spawnPipe() {
    const pipeWidth = 60;
    const pipeX = this.canvas.width;
    const gapY = Math.random() * (this.canvas.height - this.config.pipeGap - 100) + 50;
    
    // Top pipe
    const topPipe = new Pipe(pipeX, 0, pipeWidth, gapY, true);
    this.addGameObject(topPipe);
    this.pipes.push(topPipe);
    
    // Bottom pipe
    const bottomPipe = new Pipe(pipeX, gapY + this.config.pipeGap, pipeWidth, this.canvas.height - gapY - this.config.pipeGap - 50);
    this.addGameObject(bottomPipe);
    this.pipes.push(bottomPipe);
  }

  renderUI() {
    super.renderUI();
    
    if (!this.gameStarted) {
      // Start screen
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Flappy Bird', this.canvas.width / 2, this.canvas.height / 2 - 50);
      
      this.ctx.font = '24px Arial';
      this.ctx.fillText('Click or Press Space to Start', this.canvas.width / 2, this.canvas.height / 2 + 20);
      this.ctx.textAlign = 'left';
    }
    
    if (this.gameState === 'gameOver') {
      // Game over screen
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      
      this.ctx.fillStyle = '#ffffff';
      this.ctx.font = '48px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText('Game Over', this.canvas.width / 2, this.canvas.height / 2 - 50);
      
      this.ctx.font = '24px Arial';
      this.ctx.fillText(`Final Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2 + 20);
      this.ctx.fillText('Refresh to Play Again', this.canvas.width / 2, this.canvas.height / 2 + 60);
      this.ctx.textAlign = 'left';
    }
  }

  // Method to update game parameters via AI
  updateParameters(newParams) {
    if (newParams.difficulty) {
      switch (newParams.difficulty) {
        case 'easy':
          this.config.pipeGap = 200;
          this.config.pipeSpeed = 150;
          this.config.pipeSpawnRate = 2500;
          break;
        case 'medium':
          this.config.pipeGap = 150;
          this.config.pipeSpeed = 200;
          this.config.pipeSpawnRate = 2000;
          break;
        case 'hard':
          this.config.pipeGap = 120;
          this.config.pipeSpeed = 250;
          this.config.pipeSpawnRate = 1500;
          break;
      }
    }
    
    // Apply other parameter updates
    Object.assign(this.config, newParams);
  }

  // Export game data for ZIP generation
  exportGameData() {
    return {
      type: 'flappybird',
      config: this.config,
      assets: {
        // Asset URLs would be populated by AI generation
        bird: this.bird.sprite || null,
        pipes: null,
        background: null
      }
    };
  }
}
