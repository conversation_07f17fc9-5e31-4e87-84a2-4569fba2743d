/* Wizard Component Styles */

/* Common Styles */
.step-header {
  text-align: center;
  margin-bottom: 2rem;
}

.step-header h2 {
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.step-header p {
  font-size: 1.125rem;
  color: #718096;
  max-width: 600px;
  margin: 0 auto;
}

.step-actions {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid #e2e8f0;
}

.continue-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.continue-button:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
}

.continue-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.continue-hint {
  margin-top: 1rem;
  color: #718096;
  font-size: 0.875rem;
  text-align: center;
}

/* Template Selection Styles */
.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.template-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 1rem;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
  border-color: #667eea;
  background: #f7fafc;
}

.template-preview {
  height: 150px;
  background: #f7fafc;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  overflow: hidden;
  position: relative;
}

.template-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.preview-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-weight: 600;
}

.template-info h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.template-description {
  color: #718096;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.template-meta {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
}

.difficulty {
  padding: 0.25rem 0.75rem;
  border-radius: 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.difficulty.easy {
  background: #c6f6d5;
  color: #22543d;
}

.difficulty.medium {
  background: #fed7aa;
  color: #9c4221;
}

.difficulty.hard {
  background: #feb2b2;
  color: #742a2a;
}

.template-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.feature-tag {
  background: #edf2f7;
  color: #4a5568;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
}

.preview-section {
  background: #f7fafc;
  border-radius: 1rem;
  padding: 1.5rem;
  margin-top: 2rem;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.preview-controls {
  display: flex;
  gap: 0.5rem;
}

.control-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.control-button:hover:not(:disabled) {
  background: #5a67d8;
}

.control-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.preview-canvas {
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  background: white;
}

.preview-instructions {
  text-align: center;
  color: #718096;
}

/* AI Customization Styles */
.customization-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.customization-section {
  background: #f7fafc;
  border-radius: 1rem;
  padding: 1.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.section-icon {
  color: #667eea;
  width: 1.5rem;
  height: 1.5rem;
}

.section-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.style-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.style-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.style-card:hover {
  border-color: #667eea;
}

.style-card.selected {
  border-color: #667eea;
  background: #edf2f7;
}

.style-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.style-card h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.25rem;
}

.style-card p {
  font-size: 0.875rem;
  color: #718096;
  margin: 0;
}

.music-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.music-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.music-card:hover {
  border-color: #667eea;
}

.music-card.selected {
  border-color: #667eea;
  background: #edf2f7;
}

.music-card h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.music-card p {
  font-size: 0.875rem;
  color: #718096;
  margin: 0;
}

.custom-prompt {
  margin-bottom: 1.5rem;
}

.custom-prompt label {
  display: block;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.custom-prompt textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  resize: vertical;
  background: white;
  color: #2d3748;
  min-height: 80px;
  box-sizing: border-box;
}

.custom-prompt textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.custom-prompt textarea:disabled {
  background: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
}

.generate-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.generate-button:hover:not(:disabled) {
  background: #5a67d8;
}

.generate-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.button-icon {
  width: 1rem;
  height: 1rem;
}

.generation-result {
  background: #c6f6d5;
  border: 1px solid #9ae6b4;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #22543d;
  margin-bottom: 1rem;
}

.result-icon {
  width: 1rem;
  height: 1rem;
}

.asset-preview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
}

.asset-item {
  text-align: center;
}

.asset-thumbnail {
  width: 64px;
  height: 64px;
  background: #edf2f7;
  border-radius: 0.5rem;
  margin: 0 auto 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.asset-thumbnail img {
  max-width: 100%;
  max-height: 100%;
}

.asset-type {
  font-size: 0.75rem;
  color: #4a5568;
  text-transform: capitalize;
}

.audio-preview {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.audio-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 0.25rem;
}

.audio-duration,
.audio-count {
  font-size: 0.875rem;
  color: #4a5568;
}

.generation-error {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
  text-align: center;
}

.generation-error p {
  color: #c53030;
  margin-bottom: 1rem;
}

.retry-button {
  background: #c53030;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  cursor: pointer;
}

/* Parameter Tuning Styles */
.tuning-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.tuning-section {
  background: #f7fafc;
  border-radius: 1rem;
  padding: 1.5rem;
}

.difficulty-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.difficulty-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.75rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.difficulty-card:hover {
  border-color: var(--accent-color, #667eea);
}

.difficulty-card.selected {
  border-color: var(--accent-color, #667eea);
  background: #f7fafc;
}

.difficulty-card h4 {
  font-size: 1rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.difficulty-card p {
  font-size: 0.875rem;
  color: #718096;
  margin: 0;
}

.natural-language-input {
  margin-bottom: 1.5rem;
}

.natural-language-input textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 1rem;
  resize: vertical;
  background: white;
  color: #2d3748;
  min-height: 80px;
  box-sizing: border-box;
}

.natural-language-input textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.natural-language-input textarea:disabled {
  background: #f7fafc;
  color: #a0aec0;
  cursor: not-allowed;
}

.tune-button {
  background: #667eea;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tune-button:hover:not(:disabled) {
  background: #5a67d8;
}

.tune-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-requests {
  margin-top: 1rem;
}

.quick-requests p {
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 0.75rem;
}

.request-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.request-button {
  background: #edf2f7;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  padding: 0.5rem 0.75rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.request-button:hover {
  background: #e2e8f0;
  border-color: #cbd5e0;
}

.reset-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: #e2e8f0;
  color: #4a5568;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  margin-left: auto;
}

.reset-button:hover {
  background: #cbd5e0;
}

.parameters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.parameter-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.parameter-info {
  flex: 1;
}

.parameter-name {
  font-weight: 600;
  color: #2d3748;
  display: block;
}

.parameter-description {
  font-size: 0.875rem;
  color: #718096;
  display: block;
  margin-top: 0.25rem;
}

.parameter-value {
  font-weight: 600;
  color: #667eea;
  font-family: 'Courier New', monospace;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1rem;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.history-request {
  font-weight: 600;
  color: #2d3748;
}

.history-time {
  font-size: 0.875rem;
  color: #718096;
}

.history-explanation {
  font-size: 0.875rem;
  color: #4a5568;
  line-height: 1.5;
  white-space: pre-line;
}

/* Game Export Styles */
.export-sections {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.final-preview {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.final-canvas {
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  background: white;
}

.summary-section {
  background: #f7fafc;
  border-radius: 1rem;
  padding: 1.5rem;
}

.summary-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: white;
  border-radius: 0.5rem;
}

.summary-label {
  font-weight: 600;
  color: #4a5568;
}

.summary-value {
  color: #2d3748;
  font-weight: 500;
}

.export-options {
  background: #f7fafc;
  border-radius: 1rem;
  padding: 1.5rem;
}

.export-options h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.export-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-bottom: 1rem;
}

.export-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

.export-button.primary {
  background: #667eea;
  color: white;
}

.export-button.primary:hover:not(:disabled) {
  background: #5a67d8;
}

.export-button.secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.export-button.secondary:hover:not(:disabled) {
  background: #cbd5e0;
}

.export-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.export-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: #667eea;
  transition: width 0.3s ease;
}

.export-success {
  background: #c6f6d5;
  border: 1px solid #9ae6b4;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
  color: #22543d;
}

.code-preview {
  background: #f7fafc;
  border-radius: 1rem;
  padding: 1.5rem;
}

.code-preview h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 1rem;
}

.code-block {
  background: #2d3748;
  color: #e2e8f0;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  margin-bottom: 1rem;
}

.completion-message {
  text-align: center;
  padding: 2rem;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 1rem;
}

.completion-message h3 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
}

.completion-message p {
  font-size: 1.125rem;
  opacity: 0.9;
}

/* Responsive Design */
@media (max-width: 768px) {
  .template-grid {
    grid-template-columns: 1fr;
  }

  .style-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .music-grid {
    grid-template-columns: 1fr;
  }

  .difficulty-grid {
    grid-template-columns: 1fr;
  }

  .parameters-grid {
    grid-template-columns: 1fr;
  }

  .summary-grid {
    grid-template-columns: 1fr;
  }

  .export-buttons {
    flex-direction: column;
  }

  .preview-canvas,
  .final-canvas {
    max-width: 100%;
    height: auto;
  }
}