// Base GameObject class for all game entities
export class GameObject {
  constructor(x = 0, y = 0, width = 32, height = 32) {
    this.x = x;
    this.y = y;
    this.width = width;
    this.height = height;
    this.velocity = { x: 0, y: 0 };
    this.bounds = { width, height };
    this.dead = false;
    this.visible = true;
    this.physics = {
      gravity: false,
      friction: 0
    };
    this.sprite = null;
    this.color = '#ffffff';
    this.type = 'gameObject';
  }

  update(deltaTime) {
    // Override in subclasses
  }

  render(ctx) {
    if (!this.visible) return;

    if (this.sprite) {
      ctx.drawImage(this.sprite, this.x, this.y, this.width, this.height);
    } else {
      // Fallback to colored rectangle
      ctx.fillStyle = this.color;
      ctx.fillRect(this.x, this.y, this.width, this.height);
    }
  }

  onCollision(other) {
    // Override in subclasses
  }

  destroy() {
    this.dead = true;
  }

  getBounds() {
    return {
      left: this.x,
      right: this.x + this.width,
      top: this.y,
      bottom: this.y + this.height,
      centerX: this.x + this.width / 2,
      centerY: this.y + this.height / 2
    };
  }

  isOffScreen(canvasWidth, canvasHeight) {
    return this.x + this.width < 0 || 
           this.x > canvasWidth || 
           this.y + this.height < 0 || 
           this.y > canvasHeight;
  }
}

// Player character base class
export class Player extends GameObject {
  constructor(x, y, width, height) {
    super(x, y, width, height);
    this.type = 'player';
    this.health = 100;
    this.maxHealth = 100;
    this.invulnerable = false;
    this.invulnerabilityTime = 0;
    this.color = '#00ff00';
  }

  update(deltaTime) {
    super.update(deltaTime);
    
    if (this.invulnerable) {
      this.invulnerabilityTime -= deltaTime;
      if (this.invulnerabilityTime <= 0) {
        this.invulnerable = false;
      }
    }
  }

  render(ctx) {
    if (this.invulnerable) {
      // Flashing effect when invulnerable
      ctx.globalAlpha = Math.sin(Date.now() * 0.01) * 0.5 + 0.5;
    }
    
    super.render(ctx);
    ctx.globalAlpha = 1;
  }

  takeDamage(amount) {
    if (this.invulnerable) return false;
    
    this.health -= amount;
    this.invulnerable = true;
    this.invulnerabilityTime = 1; // 1 second of invulnerability
    
    if (this.health <= 0) {
      this.health = 0;
      this.destroy();
      return true; // Player died
    }
    return false;
  }

  heal(amount) {
    this.health = Math.min(this.health + amount, this.maxHealth);
  }
}

// Enemy base class
export class Enemy extends GameObject {
  constructor(x, y, width, height) {
    super(x, y, width, height);
    this.type = 'enemy';
    this.damage = 10;
    this.color = '#ff0000';
  }

  onCollision(other) {
    if (other.type === 'player') {
      other.takeDamage(this.damage);
      this.destroy();
    }
  }
}

// Collectible base class
export class Collectible extends GameObject {
  constructor(x, y, width, height, points = 10) {
    super(x, y, width, height);
    this.type = 'collectible';
    this.points = points;
    this.color = '#ffff00';
  }

  onCollision(other) {
    if (other.type === 'player') {
      // Award points (handled by game engine)
      this.destroy();
    }
  }
}

// Obstacle base class
export class Obstacle extends GameObject {
  constructor(x, y, width, height) {
    super(x, y, width, height);
    this.type = 'obstacle';
    this.color = '#8B4513';
  }

  onCollision(other) {
    if (other.type === 'player') {
      other.takeDamage(50);
    }
  }
}

// Projectile base class
export class Projectile extends GameObject {
  constructor(x, y, width, height, direction = { x: 1, y: 0 }, speed = 300) {
    super(x, y, width, height);
    this.type = 'projectile';
    this.velocity.x = direction.x * speed;
    this.velocity.y = direction.y * speed;
    this.color = '#ffffff';
    this.damage = 25;
  }

  update(deltaTime) {
    super.update(deltaTime);
    
    // Remove projectiles that go off screen
    if (this.isOffScreen(800, 600)) {
      this.destroy();
    }
  }

  onCollision(other) {
    if (other.type === 'enemy') {
      other.destroy();
      this.destroy();
    }
  }
}

// Power-up base class
export class PowerUp extends GameObject {
  constructor(x, y, width, height, effect = 'speed') {
    super(x, y, width, height);
    this.type = 'powerup';
    this.effect = effect;
    this.duration = 5; // seconds
    this.color = '#ff00ff';
  }

  onCollision(other) {
    if (other.type === 'player') {
      this.applyEffect(other);
      this.destroy();
    }
  }

  applyEffect(player) {
    // Override in specific power-up classes
    console.log(`Applied ${this.effect} power-up to player`);
  }
}
