/* GameGen App Styles */
@import './components/wizard/WizardStyles.css';

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#root {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.app {
  width: 100%;
  min-height: 100vh;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  display: flex;
  flex-direction: column;
}

.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo-icon {
  color: #667eea;
  width: 2rem;
  height: 2rem;
}

.logo h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: 700;
  color: #2d3748;
}

.tagline {
  font-size: 0.875rem;
  color: #718096;
  font-weight: 500;
}

.progress-indicator {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  position: relative;
}

.progress-step.active {
  background: #667eea;
  color: white;
}

.progress-step.completed {
  background: #48bb78;
  color: white;
}

.step-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
}

.progress-step.active .step-icon,
.progress-step.completed .step-icon {
  background: rgba(255, 255, 255, 0.3);
}

.step-title {
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
}

.step-connector {
  position: absolute;
  right: -1.5rem;
  width: 1rem;
  height: 2px;
  background: #e2e8f0;
  z-index: -1;
}

.app-main {
  flex: 1;
  width: 100%;
  padding: 2rem;
  display: flex;
  flex-direction: column;
}

.error-banner {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.error-banner button {
  background: #c53030;
  color: white;
  border: none;
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  cursor: pointer;
  font-size: 0.875rem;
}

.wizard-container {
  flex: 1;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  overflow: hidden;
  min-height: 600px;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
}

.wizard-content {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
}

.wizard-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: #f7fafc;
  border-top: 1px solid #e2e8f0;
}

.nav-button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  font-size: 0.875rem;
}

.nav-button.primary {
  background: #667eea;
  color: white;
}

.nav-button.primary:hover:not(:disabled) {
  background: #5a67d8;
}

.nav-button.secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.nav-button.secondary:hover:not(:disabled) {
  background: #cbd5e0;
}

.nav-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-spacer {
  flex: 1;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  text-align: center;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.spinner-icon {
  width: 3rem;
  height: 3rem;
  color: #667eea;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.loading-spinner p {
  margin: 0;
  color: #4a5568;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
  }

  .progress-indicator {
    flex-wrap: wrap;
    justify-content: center;
  }

  .step-title {
    display: none;
  }

  .app-main {
    padding: 1rem;
  }

  .wizard-content {
    padding: 1rem;
  }

  .wizard-navigation {
    padding: 1rem;
  }
}