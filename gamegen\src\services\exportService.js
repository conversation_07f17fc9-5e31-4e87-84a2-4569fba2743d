import J<PERSON>Z<PERSON> from 'jszip';
import { saveAs } from 'file-saver';

class ExportService {
  constructor() {
    this.exportHistory = [];
  }

  async exportGame(gameData) {
    try {
      const zip = new JSZip();
      const exportId = `export_${Date.now()}`;
      
      // Create main HTML file
      const htmlContent = this.generateGameHTML(gameData);
      zip.file('index.html', htmlContent);
      
      // Create game JavaScript
      const jsContent = this.generateGameJS(gameData);
      zip.file('game.js', jsContent);
      
      // Create CSS styles
      const cssContent = this.generateGameCSS(gameData);
      zip.file('styles.css', cssContent);
      
      // Add assets
      await this.addAssetsToZip(zip, gameData.assets);
      
      // Add audio files
      await this.addAudioToZip(zip, gameData.audio);
      
      // Add README
      const readmeContent = this.generateReadme(gameData);
      zip.file('README.md', readmeContent);
      
      // Generate and download ZIP
      const blob = await zip.generateAsync({ 
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: { level: 6 }
      });
      
      const fileName = `${gameData.template.name.replace(/\s+/g, '_').toLowerCase()}_game.zip`;
      saveAs(blob, fileName);
      
      // Record export
      this.exportHistory.push({
        id: exportId,
        gameData,
        fileName,
        timestamp: Date.now()
      });
      
      return {
        success: true,
        exportId,
        fileName,
        size: blob.size
      };
      
    } catch (error) {
      console.error('Export failed:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  generateGameHTML(gameData) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${gameData.template.name} - Created with GameGen</title>
    <link rel="stylesheet" href="styles.css">
    <meta name="description" content="${gameData.template.description}">
    <meta name="keywords" content="game, html5, javascript, ${gameData.template.name.toLowerCase()}">
    <meta name="author" content="GameGen Platform">
</head>
<body>
    <div class="game-container">
        <header class="game-header">
            <h1>${gameData.template.name}</h1>
            <div class="game-info">
                <span class="difficulty">Difficulty: ${gameData.parameters?.difficulty || 'Medium'}</span>
                <span class="style">Style: ${gameData.customizations?.visualStyle || 'Default'}</span>
            </div>
        </header>
        
        <main class="game-main">
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="600"></canvas>
                <div class="game-overlay" id="gameOverlay">
                    <div class="overlay-content">
                        <h2>Welcome to ${gameData.template.name}!</h2>
                        <p>${this.getGameInstructions(gameData.template.id)}</p>
                        <button id="startButton" class="start-button">Start Game</button>
                    </div>
                </div>
            </div>
            
            <div class="game-controls">
                <div class="control-group">
                    <button id="playPauseBtn" class="control-btn" disabled>
                        <span class="btn-icon">▶️</span>
                        <span class="btn-text">Play</span>
                    </button>
                    <button id="resetBtn" class="control-btn" disabled>
                        <span class="btn-icon">🔄</span>
                        <span class="btn-text">Reset</span>
                    </button>
                    <button id="muteBtn" class="control-btn">
                        <span class="btn-icon">🔊</span>
                        <span class="btn-text">Sound</span>
                    </button>
                </div>
                
                <div class="score-display">
                    <div class="score-item">
                        <span class="score-label">Score:</span>
                        <span id="scoreValue" class="score-value">0</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">High Score:</span>
                        <span id="highScoreValue" class="score-value">0</span>
                    </div>
                </div>
            </div>
        </main>
        
        <footer class="game-footer">
            <p>Created with <a href="https://gamegen.dev" target="_blank">GameGen</a> - No-Code Game Creation Platform</p>
            <div class="game-stats">
                <span>Visual Style: ${gameData.customizations?.visualStyle || 'Default'}</span>
                <span>Music: ${gameData.customizations?.musicStyle || 'Default'}</span>
                <span>Generated: ${new Date().toLocaleDateString()}</span>
            </div>
        </footer>
    </div>
    
    <script src="game.js"></script>
</body>
</html>`;
  }

  generateGameJS(gameData) {
    return `// ${gameData.template.name} Game
// Generated by GameGen Platform
// ${new Date().toISOString()}

class GameEngine {
    constructor(canvas, config) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.config = config;
        this.gameState = 'menu';
        this.score = 0;
        this.highScore = parseInt(localStorage.getItem('${gameData.template.id}_highScore') || '0');
        this.gameObjects = [];
        this.lastTime = 0;
        this.deltaTime = 0;
        this.muted = false;
        
        this.setupCanvas();
        this.bindEvents();
        this.updateUI();
    }
    
    setupCanvas() {
        this.canvas.width = this.config.width || 800;
        this.canvas.height = this.config.height || 600;
        this.ctx.imageSmoothingEnabled = false;
    }
    
    bindEvents() {
        // Keyboard events
        window.addEventListener('keydown', (e) => this.handleKeyDown(e));
        window.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // Mouse/Touch events
        this.canvas.addEventListener('click', (e) => this.handleClick(e));
        this.canvas.addEventListener('touchstart', (e) => this.handleTouch(e));
    }
    
    start() {
        this.gameState = 'playing';
        this.lastTime = performance.now();
        this.gameLoop();
        this.updateUI();
    }
    
    pause() {
        this.gameState = 'paused';
        this.updateUI();
    }
    
    resume() {
        this.gameState = 'playing';
        this.lastTime = performance.now();
        this.gameLoop();
        this.updateUI();
    }
    
    reset() {
        this.score = 0;
        this.gameState = 'menu';
        this.gameObjects = [];
        this.updateUI();
        this.render();
    }
    
    gameLoop(currentTime = performance.now()) {
        if (this.gameState !== 'playing') return;
        
        this.deltaTime = (currentTime - this.lastTime) / 1000;
        this.lastTime = currentTime;
        
        this.update(this.deltaTime);
        this.render();
        
        requestAnimationFrame((time) => this.gameLoop(time));
    }
    
    update(deltaTime) {
        // Game-specific update logic would go here
        // This is a simplified version
    }
    
    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Render background
        this.ctx.fillStyle = '${gameData.customizations?.backgroundColor || '#87CEEB'}';
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Render game objects
        this.gameObjects.forEach(obj => {
            if (obj.render) obj.render(this.ctx);
        });
        
        // Render UI
        this.renderUI();
    }
    
    renderUI() {
        // Score display
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '24px Arial';
        this.ctx.fillText(\`Score: \${this.score}\`, 20, 40);
        
        // Game state specific UI
        if (this.gameState === 'menu') {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = '48px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('${gameData.template.name}', this.canvas.width / 2, this.canvas.height / 2 - 50);
            this.ctx.font = '24px Arial';
            this.ctx.fillText('Click Start to Play', this.canvas.width / 2, this.canvas.height / 2 + 20);
            this.ctx.textAlign = 'left';
        }
        
        if (this.gameState === 'paused') {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = '48px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('PAUSED', this.canvas.width / 2, this.canvas.height / 2);
            this.ctx.textAlign = 'left';
        }
        
        if (this.gameState === 'gameOver') {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = '48px Arial';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('Game Over', this.canvas.width / 2, this.canvas.height / 2 - 50);
            this.ctx.font = '24px Arial';
            this.ctx.fillText(\`Final Score: \${this.score}\`, this.canvas.width / 2, this.canvas.height / 2 + 20);
            this.ctx.fillText(\`High Score: \${this.highScore}\`, this.canvas.width / 2, this.canvas.height / 2 + 60);
            this.ctx.textAlign = 'left';
        }
    }
    
    handleKeyDown(event) {
        // Game-specific key handling
        switch (event.code) {
            case 'Space':
                event.preventDefault();
                if (this.gameState === 'menu') {
                    this.start();
                } else if (this.gameState === 'playing') {
                    // Game-specific action
                }
                break;
            case 'KeyP':
                if (this.gameState === 'playing') {
                    this.pause();
                } else if (this.gameState === 'paused') {
                    this.resume();
                }
                break;
            case 'KeyR':
                this.reset();
                break;
        }
    }
    
    handleKeyUp(event) {
        // Handle key releases
    }
    
    handleClick(event) {
        if (this.gameState === 'menu') {
            this.start();
        }
        // Game-specific click handling
    }
    
    handleTouch(event) {
        event.preventDefault();
        this.handleClick(event);
    }
    
    updateScore(points) {
        this.score += points;
        if (this.score > this.highScore) {
            this.highScore = this.score;
            localStorage.setItem('${gameData.template.id}_highScore', this.highScore.toString());
        }
        this.updateUI();
    }
    
    gameOver() {
        this.gameState = 'gameOver';
        this.updateUI();
    }
    
    updateUI() {
        const scoreElement = document.getElementById('scoreValue');
        const highScoreElement = document.getElementById('highScoreValue');
        const playPauseBtn = document.getElementById('playPauseBtn');
        const resetBtn = document.getElementById('resetBtn');
        const overlay = document.getElementById('gameOverlay');
        
        if (scoreElement) scoreElement.textContent = this.score;
        if (highScoreElement) highScoreElement.textContent = this.highScore;
        
        if (playPauseBtn) {
            playPauseBtn.disabled = this.gameState === 'menu' || this.gameState === 'gameOver';
            const icon = playPauseBtn.querySelector('.btn-icon');
            const text = playPauseBtn.querySelector('.btn-text');
            if (this.gameState === 'playing') {
                icon.textContent = '⏸️';
                text.textContent = 'Pause';
            } else {
                icon.textContent = '▶️';
                text.textContent = 'Play';
            }
        }
        
        if (resetBtn) {
            resetBtn.disabled = this.gameState === 'menu';
        }
        
        if (overlay) {
            overlay.style.display = this.gameState === 'menu' ? 'flex' : 'none';
        }
    }
}

// Game configuration from customization
const gameConfig = ${JSON.stringify(gameData.parameters, null, 2)};

// Initialize game when page loads
window.addEventListener('DOMContentLoaded', () => {
    const canvas = document.getElementById('gameCanvas');
    const game = new GameEngine(canvas, gameConfig);
    
    // Bind control buttons
    document.getElementById('startButton').addEventListener('click', () => {
        game.start();
    });
    
    document.getElementById('playPauseBtn').addEventListener('click', () => {
        if (game.gameState === 'playing') {
            game.pause();
        } else if (game.gameState === 'paused') {
            game.resume();
        }
    });
    
    document.getElementById('resetBtn').addEventListener('click', () => {
        game.reset();
    });
    
    document.getElementById('muteBtn').addEventListener('click', () => {
        game.muted = !game.muted;
        const icon = document.querySelector('#muteBtn .btn-icon');
        icon.textContent = game.muted ? '🔇' : '🔊';
    });
    
    // Make game globally accessible for debugging
    window.game = game;
});`;
  }

  generateGameCSS(gameData) {
    return `/* ${gameData.template.name} Game Styles */
/* Generated by GameGen Platform */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.game-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    max-width: 900px;
    width: 100%;
}

.game-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 20px;
    text-align: center;
}

.game-header h1 {
    font-size: 2rem;
    margin-bottom: 10px;
}

.game-info {
    display: flex;
    justify-content: center;
    gap: 20px;
    font-size: 0.9rem;
    opacity: 0.9;
}

.game-main {
    padding: 20px;
}

.canvas-container {
    position: relative;
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

#gameCanvas {
    border: 3px solid #333;
    border-radius: 10px;
    background: #f0f0f0;
    max-width: 100%;
    height: auto;
}

.game-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
}

.overlay-content {
    text-align: center;
    color: white;
    padding: 20px;
}

.overlay-content h2 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.overlay-content p {
    font-size: 1.1rem;
    margin-bottom: 25px;
    line-height: 1.5;
}

.start-button {
    background: #667eea;
    color: white;
    border: none;
    padding: 15px 30px;
    font-size: 1.1rem;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.start-button:hover {
    background: #5a67d8;
    transform: translateY(-2px);
}

.game-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.control-group {
    display: flex;
    gap: 10px;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: #667eea;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.control-btn:hover:not(:disabled) {
    background: #5a67d8;
    transform: translateY(-1px);
}

.control-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.btn-icon {
    font-size: 1rem;
}

.score-display {
    display: flex;
    gap: 20px;
}

.score-item {
    text-align: center;
}

.score-label {
    display: block;
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 2px;
}

.score-value {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #333;
}

.game-footer {
    background: #f8f9fa;
    padding: 15px 20px;
    text-align: center;
    border-top: 1px solid #e9ecef;
}

.game-footer p {
    margin-bottom: 10px;
    color: #666;
}

.game-footer a {
    color: #667eea;
    text-decoration: none;
}

.game-footer a:hover {
    text-decoration: underline;
}

.game-stats {
    display: flex;
    justify-content: center;
    gap: 15px;
    font-size: 0.8rem;
    color: #888;
    flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }
    
    .game-header h1 {
        font-size: 1.5rem;
    }
    
    .game-info {
        flex-direction: column;
        gap: 5px;
    }
    
    .game-main {
        padding: 15px;
    }
    
    .game-controls {
        flex-direction: column;
        align-items: stretch;
    }
    
    .control-group {
        justify-content: center;
    }
    
    .score-display {
        justify-content: center;
    }
    
    .game-stats {
        flex-direction: column;
        gap: 5px;
    }
}

@media (max-width: 480px) {
    .overlay-content h2 {
        font-size: 1.5rem;
    }
    
    .overlay-content p {
        font-size: 1rem;
    }
    
    .start-button {
        padding: 12px 25px;
        font-size: 1rem;
    }
    
    .control-btn {
        padding: 8px 12px;
        font-size: 0.8rem;
    }
}`;
  }

  getGameInstructions(gameId) {
    const instructions = {
      'flappybird': 'Click or press Space to flap wings and avoid the pipes!',
      'speedrunner': 'Use arrow keys to move and Space to jump. Collect coins and avoid obstacles!',
      'whackthemole': 'Click on moles when they appear to score points!',
      'match3': 'Click gems to select them, then match 3 or more of the same type!',
      'crossyroad': 'Use arrow keys or swipe to move. Cross roads and rivers safely!'
    };
    
    return instructions[gameId] || 'Follow the on-screen instructions to play!';
  }

  async addAssetsToZip(zip, assets) {
    if (!assets || !assets.assets) return;
    
    const assetsFolder = zip.folder('assets');
    
    for (const [type, asset] of Object.entries(assets.assets)) {
      if (asset.placeholder) {
        try {
          // Convert data URL to blob
          const response = await fetch(asset.placeholder);
          const blob = await response.blob();
          assetsFolder.file(`${type}.png`, blob);
        } catch (error) {
          console.warn(`Failed to add asset ${type}:`, error);
        }
      }
    }
  }

  async addAudioToZip(zip, audio) {
    if (!audio) return;
    
    const audioFolder = zip.folder('audio');
    
    // Add placeholder audio files
    audioFolder.file('background.wav', 'placeholder audio data');
    
    if (audio.soundEffects) {
      Object.keys(audio.soundEffects).forEach(effectType => {
        audioFolder.file(`${effectType}.wav`, 'placeholder audio data');
      });
    }
  }

  generateReadme(gameData) {
    return `# ${gameData.template.name}

Created with **GameGen** - No-Code Game Creation Platform

## 🎮 Game Description

${gameData.template.description}

## 🎯 How to Play

${this.getGameInstructions(gameData.template.id)}

### Controls
- **Keyboard**: Arrow keys for movement, Space for action
- **Mouse**: Click to interact
- **Touch**: Tap and swipe on mobile devices

## 📁 Files Included

- \`index.html\` - Main game file (open this in a web browser)
- \`game.js\` - Game logic and engine
- \`styles.css\` - Game styling
- \`assets/\` - Game graphics and sprites
- \`audio/\` - Background music and sound effects
- \`README.md\` - This file

## 🎨 Customizations Applied

- **Visual Style**: ${gameData.customizations?.visualStyle || 'Default'}
- **Music Style**: ${gameData.customizations?.musicStyle || 'Default'}
- **Difficulty**: ${gameData.parameters?.difficulty || 'Medium'}

## 🚀 How to Run

1. Extract all files to a folder
2. Open \`index.html\` in a modern web browser
3. Start playing!

## 📱 Compatibility

- ✅ Chrome, Firefox, Safari, Edge (latest versions)
- ✅ Desktop and mobile devices
- ✅ Works offline (no internet required)
- ✅ Responsive design

## 🛠️ Technical Details

- Built with HTML5 Canvas and JavaScript
- No external dependencies
- Responsive design for all screen sizes
- Local storage for high scores

## 🎉 About GameGen

This game was created using GameGen, a no-code game creation platform that uses AI to help anyone create games without programming knowledge.

Visit [GameGen](https://gamegen.dev) to create your own games!

---

**Generated on**: ${new Date().toLocaleDateString()}
**Game ID**: ${gameData.template.id}
**Export Version**: 1.0

Enjoy your game! 🎮
`;
  }

  getExportHistory() {
    return [...this.exportHistory];
  }

  clearHistory() {
    this.exportHistory = [];
  }
}

export default new ExportService();
