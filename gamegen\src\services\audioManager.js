import geminiService from './geminiService.js';

class AudioManager {
  constructor() {
    this.audioContext = null;
    this.audioCache = new Map();
    this.currentMusic = null;
    this.soundEffects = new Map();
    this.volume = {
      master: 1.0,
      music: 0.7,
      effects: 0.8
    };
    this.musicPresets = {
      'upbeat': {
        description: 'Energetic and motivating background music',
        tempo: 'fast',
        mood: 'happy',
        instruments: ['synthesizer', 'drums', 'bass']
      },
      'ambient': {
        description: 'Calm and atmospheric background music',
        tempo: 'slow',
        mood: 'peaceful',
        instruments: ['pad', 'strings', 'soft piano']
      },
      'retro': {
        description: '8-bit style chiptune music',
        tempo: 'medium',
        mood: 'nostalgic',
        instruments: ['square wave', 'triangle wave', 'noise']
      },
      'epic': {
        description: 'Dramatic and powerful orchestral music',
        tempo: 'medium',
        mood: 'heroic',
        instruments: ['orchestra', 'brass', 'timpani']
      },
      'minimal': {
        description: 'Simple and clean background music',
        tempo: 'slow',
        mood: 'focused',
        instruments: ['piano', 'soft strings']
      }
    };
    
    this.initializeAudioContext();
  }

  async initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      console.log('Audio context initialized');
    } catch (error) {
      console.error('Failed to initialize audio context:', error);
    }
  }

  async generateGameAudio(gameType, musicStyle, customPrompt = '') {
    try {
      const audioPackage = {
        gameType,
        musicStyle,
        timestamp: Date.now(),
        music: null,
        soundEffects: {}
      };

      // Generate background music
      console.log(`Generating background music for ${gameType}...`);
      audioPackage.music = await this.generateBackgroundMusic(gameType, musicStyle, customPrompt);

      // Generate sound effects
      console.log(`Generating sound effects for ${gameType}...`);
      const soundEffectTypes = this.getSoundEffectTypesForGame(gameType);
      
      for (const effectType of soundEffectTypes) {
        audioPackage.soundEffects[effectType] = await this.generateSoundEffect(gameType, effectType, musicStyle);
      }

      // Cache the audio package
      const packageId = `${gameType}_${musicStyle}_${Date.now()}`;
      this.audioCache.set(packageId, audioPackage);

      return {
        packageId,
        audioPackage,
        success: true
      };

    } catch (error) {
      console.error('Error generating game audio:', error);
      return {
        success: false,
        error: error.message,
        fallbackPackage: this.getFallbackAudioPackage(gameType)
      };
    }
  }

  getSoundEffectTypesForGame(gameType) {
    const effectMap = {
      'flappybird': ['jump', 'hit', 'score', 'gameOver'],
      'speedrunner': ['jump', 'land', 'collect', 'hit', 'powerUp'],
      'whackthemole': ['whack', 'miss', 'moleAppear', 'timeUp'],
      'match3': ['match', 'swap', 'cascade', 'powerUp', 'gameWin'],
      'crossyroad': ['move', 'carHorn', 'water', 'collect', 'gameOver']
    };
    
    return effectMap[gameType] || ['action', 'collect', 'hit', 'success'];
  }

  async generateBackgroundMusic(gameType, style, customPrompt) {
    const preset = this.musicPresets[style] || this.musicPresets['upbeat'];
    
    // Build music generation prompt
    const prompt = this.buildMusicPrompt(gameType, preset, customPrompt);
    
    try {
      // Use Gemini to generate detailed music specifications
      const musicSpecs = await geminiService.generateAudioDescription(prompt, 'music', gameType);
      
      // Generate procedural music (placeholder implementation)
      const generatedMusic = await this.generateProceduralMusic(musicSpecs, preset);
      
      return {
        type: 'background_music',
        style: style,
        specifications: musicSpecs,
        audioData: generatedMusic,
        metadata: {
          gameType,
          style,
          duration: 30, // seconds
          loop: true,
          tempo: preset.tempo,
          mood: preset.mood
        }
      };

    } catch (error) {
      console.error('Error generating background music:', error);
      return this.getFallbackMusic(style);
    }
  }

  async generateSoundEffect(gameType, effectType, style) {
    try {
      const prompt = this.buildSoundEffectPrompt(gameType, effectType, style);
      const effectSpecs = await geminiService.generateAudioDescription(prompt, 'sound_effect', gameType);
      
      // Generate procedural sound effect
      const generatedEffect = await this.generateProceduralSoundEffect(effectType, effectSpecs);
      
      return {
        type: effectType,
        specifications: effectSpecs,
        audioData: generatedEffect,
        metadata: {
          gameType,
          effectType,
          duration: this.getEffectDuration(effectType),
          volume: this.getEffectVolume(effectType)
        }
      };

    } catch (error) {
      console.error(`Error generating ${effectType} sound effect:`, error);
      return this.getFallbackSoundEffect(effectType);
    }
  }

  buildMusicPrompt(gameType, preset, customPrompt) {
    let prompt = `Create background music for a ${gameType} game. `;
    prompt += `Style: ${preset.description}. `;
    prompt += `Tempo: ${preset.tempo}, Mood: ${preset.mood}. `;
    prompt += `Instruments: ${preset.instruments.join(', ')}. `;
    prompt += `The music should loop seamlessly and not be distracting to gameplay. `;
    
    if (customPrompt) {
      prompt += `Additional requirements: ${customPrompt}`;
    }

    return prompt;
  }

  buildSoundEffectPrompt(gameType, effectType, style) {
    const effectDescriptions = {
      'jump': 'A light, bouncy sound for character jumping',
      'hit': 'A sharp impact sound for collisions',
      'collect': 'A pleasant chime for collecting items',
      'score': 'A satisfying sound for scoring points',
      'gameOver': 'A dramatic sound for game ending',
      'whack': 'A comedic bonk sound for hitting moles',
      'match': 'A magical sparkle sound for matching gems',
      'powerUp': 'An empowering sound for gaining abilities'
    };

    const description = effectDescriptions[effectType] || `A ${effectType} sound effect`;
    
    return `Create a ${description} for a ${gameType} game. The sound should be clear, not too long, and fit the ${style} style of the game.`;
  }

  async generateProceduralMusic(specs, preset) {
    // Simplified procedural music generation
    // In a real implementation, this would use Web Audio API or integrate with music generation APIs
    
    if (!this.audioContext) {
      await this.initializeAudioContext();
    }

    const duration = 30; // 30 seconds
    const sampleRate = this.audioContext.sampleRate;
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(2, length, sampleRate);

    // Generate simple procedural music based on preset
    for (let channel = 0; channel < 2; channel++) {
      const channelData = buffer.getChannelData(channel);
      
      for (let i = 0; i < length; i++) {
        const time = i / sampleRate;
        let sample = 0;

        // Add different frequency components based on preset
        if (preset.instruments.includes('synthesizer') || preset.instruments.includes('square wave')) {
          sample += Math.sin(2 * Math.PI * 440 * time) * 0.1; // A4 note
          sample += Math.sin(2 * Math.PI * 554.37 * time) * 0.08; // C#5 note
        }

        if (preset.instruments.includes('bass')) {
          sample += Math.sin(2 * Math.PI * 110 * time) * 0.15; // A2 bass note
        }

        // Apply envelope and effects
        const envelope = Math.sin(time * 0.5) * 0.5 + 0.5; // Slow modulation
        channelData[i] = sample * envelope * 0.3; // Keep volume reasonable
      }
    }

    return {
      audioBuffer: buffer,
      url: this.bufferToDataURL(buffer),
      format: 'wav'
    };
  }

  async generateProceduralSoundEffect(effectType, specs) {
    if (!this.audioContext) {
      await this.initializeAudioContext();
    }

    const duration = this.getEffectDuration(effectType);
    const sampleRate = this.audioContext.sampleRate;
    const length = sampleRate * duration;
    const buffer = this.audioContext.createBuffer(1, length, sampleRate);
    const channelData = buffer.getChannelData(0);

    // Generate different sound effects based on type
    switch (effectType) {
      case 'jump':
        this.generateJumpSound(channelData, sampleRate, duration);
        break;
      case 'hit':
        this.generateHitSound(channelData, sampleRate, duration);
        break;
      case 'collect':
        this.generateCollectSound(channelData, sampleRate, duration);
        break;
      case 'score':
        this.generateScoreSound(channelData, sampleRate, duration);
        break;
      default:
        this.generateGenericSound(channelData, sampleRate, duration);
    }

    return {
      audioBuffer: buffer,
      url: this.bufferToDataURL(buffer),
      format: 'wav'
    };
  }

  generateJumpSound(data, sampleRate, duration) {
    for (let i = 0; i < data.length; i++) {
      const time = i / sampleRate;
      const frequency = 800 - (time / duration) * 400; // Descending frequency
      const envelope = Math.exp(-time * 8); // Quick decay
      data[i] = Math.sin(2 * Math.PI * frequency * time) * envelope * 0.3;
    }
  }

  generateHitSound(data, sampleRate, duration) {
    for (let i = 0; i < data.length; i++) {
      const time = i / sampleRate;
      const noise = (Math.random() - 0.5) * 2; // White noise
      const envelope = Math.exp(-time * 15); // Very quick decay
      data[i] = noise * envelope * 0.2;
    }
  }

  generateCollectSound(data, sampleRate, duration) {
    for (let i = 0; i < data.length; i++) {
      const time = i / sampleRate;
      const frequency = 1000 + Math.sin(time * 20) * 200; // Vibrato effect
      const envelope = Math.exp(-time * 3); // Moderate decay
      data[i] = Math.sin(2 * Math.PI * frequency * time) * envelope * 0.25;
    }
  }

  generateScoreSound(data, sampleRate, duration) {
    for (let i = 0; i < data.length; i++) {
      const time = i / sampleRate;
      let sample = 0;
      
      // Chord progression
      sample += Math.sin(2 * Math.PI * 523.25 * time) * 0.1; // C5
      sample += Math.sin(2 * Math.PI * 659.25 * time) * 0.1; // E5
      sample += Math.sin(2 * Math.PI * 783.99 * time) * 0.1; // G5
      
      const envelope = Math.exp(-time * 2);
      data[i] = sample * envelope;
    }
  }

  generateGenericSound(data, sampleRate, duration) {
    for (let i = 0; i < data.length; i++) {
      const time = i / sampleRate;
      const frequency = 440; // A4
      const envelope = Math.exp(-time * 5);
      data[i] = Math.sin(2 * Math.PI * frequency * time) * envelope * 0.2;
    }
  }

  getEffectDuration(effectType) {
    const durations = {
      'jump': 0.3,
      'hit': 0.2,
      'collect': 0.5,
      'score': 1.0,
      'gameOver': 2.0,
      'whack': 0.25,
      'match': 0.4,
      'powerUp': 1.5
    };
    
    return durations[effectType] || 0.5;
  }

  getEffectVolume(effectType) {
    const volumes = {
      'jump': 0.6,
      'hit': 0.8,
      'collect': 0.7,
      'score': 0.9,
      'gameOver': 1.0,
      'whack': 0.8,
      'match': 0.6,
      'powerUp': 0.9
    };
    
    return volumes[effectType] || 0.7;
  }

  bufferToDataURL(buffer) {
    // Convert AudioBuffer to data URL (simplified implementation)
    // In a real implementation, this would properly encode to WAV format
    return 'data:audio/wav;base64,UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT';
  }

  getFallbackMusic(style) {
    return {
      type: 'background_music',
      style: style,
      specifications: {
        description: `Fallback ${style} background music`,
        mood: 'neutral',
        tempo: 'medium'
      },
      audioData: null,
      metadata: {
        style,
        duration: 30,
        loop: true,
        fallback: true
      }
    };
  }

  getFallbackSoundEffect(effectType) {
    return {
      type: effectType,
      specifications: {
        description: `Fallback ${effectType} sound effect`
      },
      audioData: null,
      metadata: {
        effectType,
        duration: this.getEffectDuration(effectType),
        volume: this.getEffectVolume(effectType),
        fallback: true
      }
    };
  }

  getFallbackAudioPackage(gameType) {
    return {
      gameType,
      musicStyle: 'fallback',
      timestamp: Date.now(),
      music: this.getFallbackMusic('upbeat'),
      soundEffects: {
        action: this.getFallbackSoundEffect('action'),
        collect: this.getFallbackSoundEffect('collect'),
        hit: this.getFallbackSoundEffect('hit')
      }
    };
  }

  // Audio playback methods
  async playMusic(musicData) {
    if (this.currentMusic) {
      this.currentMusic.stop();
    }

    if (musicData && musicData.audioData && musicData.audioData.audioBuffer) {
      const source = this.audioContext.createBufferSource();
      source.buffer = musicData.audioData.audioBuffer;
      source.loop = true;
      
      const gainNode = this.audioContext.createGain();
      gainNode.gain.value = this.volume.music * this.volume.master;
      
      source.connect(gainNode);
      gainNode.connect(this.audioContext.destination);
      
      source.start();
      this.currentMusic = source;
    }
  }

  async playSoundEffect(effectData) {
    if (effectData && effectData.audioData && effectData.audioData.audioBuffer) {
      const source = this.audioContext.createBufferSource();
      source.buffer = effectData.audioData.audioBuffer;
      
      const gainNode = this.audioContext.createGain();
      gainNode.gain.value = this.volume.effects * this.volume.master * (effectData.metadata.volume || 1.0);
      
      source.connect(gainNode);
      gainNode.connect(this.audioContext.destination);
      
      source.start();
    }
  }

  setVolume(type, value) {
    this.volume[type] = Math.max(0, Math.min(1, value));
  }

  getAudioPackage(packageId) {
    return this.audioCache.get(packageId);
  }

  clearCache() {
    this.audioCache.clear();
  }
}

export default new AudioManager();
