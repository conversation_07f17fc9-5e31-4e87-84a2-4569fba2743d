<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flappy Bird - Created with GameGen</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .game-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }

        .game-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .game-header h1 {
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .game-info {
            display: flex;
            justify-content: center;
            gap: 20px;
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .game-main {
            padding: 20px;
        }

        .canvas-container {
            position: relative;
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }

        #gameCanvas {
            border: 3px solid #333;
            border-radius: 10px;
            background: #87CEEB;
            max-width: 100%;
            height: auto;
        }

        .game-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 10px;
        }

        .overlay-content {
            text-align: center;
            color: white;
            padding: 20px;
        }

        .overlay-content h2 {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .overlay-content p {
            font-size: 1.1rem;
            margin-bottom: 25px;
            line-height: 1.5;
        }

        .start-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 15px 30px;
            font-size: 1.1rem;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .start-button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
        }

        .game-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .control-group {
            display: flex;
            gap: 10px;
        }

        .control-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 15px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.9rem;
        }

        .control-btn:hover:not(:disabled) {
            background: #5a67d8;
            transform: translateY(-1px);
        }

        .control-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .score-display {
            display: flex;
            gap: 20px;
        }

        .score-item {
            text-align: center;
        }

        .score-label {
            display: block;
            font-size: 0.8rem;
            color: #666;
            margin-bottom: 2px;
        }

        .score-value {
            display: block;
            font-size: 1.2rem;
            font-weight: bold;
            color: #333;
        }

        .game-footer {
            background: #f8f9fa;
            padding: 15px 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }

        .game-footer p {
            margin-bottom: 10px;
            color: #666;
        }

        .game-footer a {
            color: #667eea;
            text-decoration: none;
        }

        .game-footer a:hover {
            text-decoration: underline;
        }

        .game-stats {
            display: flex;
            justify-content: center;
            gap: 15px;
            font-size: 0.8rem;
            color: #888;
            flex-wrap: wrap;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .game-header h1 {
                font-size: 1.5rem;
            }
            
            .game-info {
                flex-direction: column;
                gap: 5px;
            }
            
            .game-controls {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group {
                justify-content: center;
            }
            
            .score-display {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <header class="game-header">
            <h1>Flappy Bird</h1>
            <div class="game-info">
                <span class="difficulty">Difficulty: Medium</span>
                <span class="style">Style: Cartoon</span>
            </div>
        </header>
        
        <main class="game-main">
            <div class="canvas-container">
                <canvas id="gameCanvas" width="800" height="600"></canvas>
                <div class="game-overlay" id="gameOverlay">
                    <div class="overlay-content">
                        <h2>Welcome to Flappy Bird!</h2>
                        <p>Click or press Space to flap wings and avoid the pipes!</p>
                        <button id="startButton" class="start-button">Start Game</button>
                    </div>
                </div>
            </div>
            
            <div class="game-controls">
                <div class="control-group">
                    <button id="playPauseBtn" class="control-btn" disabled>
                        <span class="btn-icon">▶️</span>
                        <span class="btn-text">Play</span>
                    </button>
                    <button id="resetBtn" class="control-btn" disabled>
                        <span class="btn-icon">🔄</span>
                        <span class="btn-text">Reset</span>
                    </button>
                    <button id="muteBtn" class="control-btn">
                        <span class="btn-icon">🔊</span>
                        <span class="btn-text">Sound</span>
                    </button>
                </div>
                
                <div class="score-display">
                    <div class="score-item">
                        <span class="score-label">Score:</span>
                        <span id="scoreValue" class="score-value">0</span>
                    </div>
                    <div class="score-item">
                        <span class="score-label">High Score:</span>
                        <span id="highScoreValue" class="score-value">0</span>
                    </div>
                </div>
            </div>
        </main>
        
        <footer class="game-footer">
            <p>Created with <a href="https://gamegen.dev" target="_blank">GameGen</a> - No-Code Game Creation Platform</p>
            <div class="game-stats">
                <span>Visual Style: Cartoon</span>
                <span>Music: Upbeat</span>
                <span>Generated: 12/7/2024</span>
            </div>
        </footer>
    </div>
    
    <script>
        // Simple Flappy Bird Game - Generated by GameGen
        class FlappyBirdGame {
            constructor(canvas) {
                this.canvas = canvas;
                this.ctx = canvas.getContext('2d');
                this.gameState = 'menu';
                this.score = 0;
                this.highScore = parseInt(localStorage.getItem('flappybird_highScore') || '0');
                
                this.bird = {
                    x: 100,
                    y: 300,
                    width: 40,
                    height: 30,
                    velocity: 0,
                    gravity: 0.5,
                    jumpForce: -10
                };
                
                this.pipes = [];
                this.pipeGap = 150;
                this.pipeSpeed = 2;
                this.lastPipeSpawn = 0;
                
                this.bindEvents();
                this.updateUI();
                this.gameLoop();
            }
            
            bindEvents() {
                window.addEventListener('keydown', (e) => {
                    if (e.code === 'Space') {
                        e.preventDefault();
                        this.jump();
                    }
                });
                
                this.canvas.addEventListener('click', () => {
                    this.jump();
                });
            }
            
            jump() {
                if (this.gameState === 'menu') {
                    this.start();
                } else if (this.gameState === 'playing') {
                    this.bird.velocity = this.bird.jumpForce;
                }
            }
            
            start() {
                this.gameState = 'playing';
                this.score = 0;
                this.bird.y = 300;
                this.bird.velocity = 0;
                this.pipes = [];
                this.lastPipeSpawn = 0;
                this.updateUI();
            }
            
            reset() {
                this.gameState = 'menu';
                this.score = 0;
                this.bird.y = 300;
                this.bird.velocity = 0;
                this.pipes = [];
                this.updateUI();
            }
            
            gameLoop() {
                this.update();
                this.render();
                requestAnimationFrame(() => this.gameLoop());
            }
            
            update() {
                if (this.gameState !== 'playing') return;
                
                // Update bird
                this.bird.velocity += this.bird.gravity;
                this.bird.y += this.bird.velocity;
                
                // Spawn pipes
                this.lastPipeSpawn++;
                if (this.lastPipeSpawn > 120) { // Every 2 seconds at 60fps
                    this.spawnPipe();
                    this.lastPipeSpawn = 0;
                }
                
                // Update pipes
                this.pipes.forEach(pipe => {
                    pipe.x -= this.pipeSpeed;
                    
                    // Score when passing pipe
                    if (!pipe.scored && pipe.x + pipe.width < this.bird.x) {
                        pipe.scored = true;
                        if (pipe.type === 'top') {
                            this.score++;
                            this.updateUI();
                        }
                    }
                });
                
                // Remove off-screen pipes
                this.pipes = this.pipes.filter(pipe => pipe.x + pipe.width > 0);
                
                // Check collisions
                this.checkCollisions();
                
                // Check bounds
                if (this.bird.y < 0 || this.bird.y + this.bird.height > this.canvas.height) {
                    this.gameOver();
                }
            }
            
            spawnPipe() {
                const gapY = Math.random() * (this.canvas.height - this.pipeGap - 100) + 50;
                
                // Top pipe
                this.pipes.push({
                    x: this.canvas.width,
                    y: 0,
                    width: 60,
                    height: gapY,
                    type: 'top',
                    scored: false
                });
                
                // Bottom pipe
                this.pipes.push({
                    x: this.canvas.width,
                    y: gapY + this.pipeGap,
                    width: 60,
                    height: this.canvas.height - gapY - this.pipeGap,
                    type: 'bottom',
                    scored: false
                });
            }
            
            checkCollisions() {
                this.pipes.forEach(pipe => {
                    if (this.bird.x < pipe.x + pipe.width &&
                        this.bird.x + this.bird.width > pipe.x &&
                        this.bird.y < pipe.y + pipe.height &&
                        this.bird.y + this.bird.height > pipe.y) {
                        this.gameOver();
                    }
                });
            }
            
            gameOver() {
                this.gameState = 'gameOver';
                if (this.score > this.highScore) {
                    this.highScore = this.score;
                    localStorage.setItem('flappybird_highScore', this.highScore.toString());
                }
                this.updateUI();
                
                setTimeout(() => {
                    this.reset();
                }, 2000);
            }
            
            render() {
                // Clear canvas
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
                
                // Background
                this.ctx.fillStyle = '#87CEEB';
                this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                
                // Bird
                this.ctx.fillStyle = '#FFD700';
                this.ctx.fillRect(this.bird.x, this.bird.y, this.bird.width, this.bird.height);
                
                // Pipes
                this.ctx.fillStyle = '#228B22';
                this.pipes.forEach(pipe => {
                    this.ctx.fillRect(pipe.x, pipe.y, pipe.width, pipe.height);
                });
                
                // UI
                this.ctx.fillStyle = '#ffffff';
                this.ctx.font = '24px Arial';
                this.ctx.fillText(`Score: ${this.score}`, 20, 40);
                
                if (this.gameState === 'menu') {
                    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                    this.ctx.fillStyle = '#ffffff';
                    this.ctx.font = '48px Arial';
                    this.ctx.textAlign = 'center';
                    this.ctx.fillText('Flappy Bird', this.canvas.width / 2, this.canvas.height / 2 - 50);
                    this.ctx.font = '24px Arial';
                    this.ctx.fillText('Click to Start', this.canvas.width / 2, this.canvas.height / 2 + 20);
                    this.ctx.textAlign = 'left';
                }
                
                if (this.gameState === 'gameOver') {
                    this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
                    this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
                    this.ctx.fillStyle = '#ffffff';
                    this.ctx.font = '48px Arial';
                    this.ctx.textAlign = 'center';
                    this.ctx.fillText('Game Over', this.canvas.width / 2, this.canvas.height / 2 - 50);
                    this.ctx.font = '24px Arial';
                    this.ctx.fillText(`Score: ${this.score}`, this.canvas.width / 2, this.canvas.height / 2 + 20);
                    this.ctx.textAlign = 'left';
                }
            }
            
            updateUI() {
                const scoreElement = document.getElementById('scoreValue');
                const highScoreElement = document.getElementById('highScoreValue');
                const overlay = document.getElementById('gameOverlay');
                
                if (scoreElement) scoreElement.textContent = this.score;
                if (highScoreElement) highScoreElement.textContent = this.highScore;
                if (overlay) overlay.style.display = this.gameState === 'menu' ? 'flex' : 'none';
            }
        }
        
        // Initialize game when page loads
        window.addEventListener('DOMContentLoaded', () => {
            const canvas = document.getElementById('gameCanvas');
            const game = new FlappyBirdGame(canvas);
            
            // Bind control buttons
            document.getElementById('startButton').addEventListener('click', () => {
                game.start();
            });
            
            document.getElementById('resetBtn').addEventListener('click', () => {
                game.reset();
            });
            
            document.getElementById('muteBtn').addEventListener('click', () => {
                // Toggle mute functionality would go here
                const icon = document.querySelector('#muteBtn .btn-icon');
                icon.textContent = icon.textContent === '🔊' ? '🔇' : '🔊';
            });
        });
    </script>
</body>
</html>
