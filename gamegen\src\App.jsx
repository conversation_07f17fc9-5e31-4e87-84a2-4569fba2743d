import { useState, useEffect } from 'react';
import { Gamepad2, <PERSON>rk<PERSON>, Settings, Download } from 'lucide-react';
import './App.css';

// Temporarily comment out imports to debug
// import TemplateSelection from './components/wizard/TemplateSelection';
// import AICustomization from './components/wizard/AICustomization';
// import ParameterTuning from './components/wizard/ParameterTuning';
// import GameExport from './components/wizard/GameExport';

// Import services
// import assetGenerator from './services/assetGenerator';
// import parameterTuner from './services/parameterTuner';
// import audioManager from './services/audioManager';

function App() {
  const [currentStep, setCurrentStep] = useState(1);
  const [gameData, setGameData] = useState({
    template: null,
    assets: null,
    parameters: null,
    audio: null,
    customizations: {}
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Temporarily simplified for debugging
  console.log('App component rendering...');

  // Simplified return for debugging
  return (
    <div className="app" style={{ padding: '20px', color: 'white', minHeight: '100vh' }}>
      <h1>🎮 GameGen Platform</h1>
      <p>Debug Mode - React is working!</p>

      <div style={{ background: 'rgba(255,255,255,0.1)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>
        <h2>🔧 Debug Information</h2>
        <p>✅ React components loading</p>
        <p>✅ State management working</p>
        <p>✅ CSS styles applying</p>
        <p>Current Step: {currentStep}</p>
        <p>Loading: {loading ? 'Yes' : 'No'}</p>
        <p>Error: {error || 'None'}</p>
      </div>

      <div style={{ background: 'rgba(255,255,255,0.1)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>
        <h2>🚀 Next Steps</h2>
        <p>1. Verify this page loads correctly</p>
        <p>2. Check browser console for any errors</p>
        <p>3. Re-enable wizard components gradually</p>

        <button
          onClick={() => setCurrentStep(currentStep + 1)}
          style={{
            padding: '10px 20px',
            margin: '10px',
            background: '#667eea',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Test State Update (Step: {currentStep})
        </button>

        <button
          onClick={() => setError(error ? null : 'Test error message')}
          style={{
            padding: '10px 20px',
            margin: '10px',
            background: '#e53e3e',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Test Error Toggle
        </button>
      </div>

      <div style={{ background: 'rgba(255,255,255,0.1)', padding: '20px', borderRadius: '10px', margin: '20px 0' }}>
        <h2>📊 Environment Check</h2>
        <p>API Key Available: {import.meta.env.VITE_GEMINI_API_KEY ? '✅ Yes' : '❌ No'}</p>
        <p>Development Mode: {import.meta.env.DEV ? '✅ Yes' : '❌ No'}</p>
        <p>Base URL: {import.meta.env.BASE_URL}</p>
      </div>
    </div>
  );
}

export default App;
