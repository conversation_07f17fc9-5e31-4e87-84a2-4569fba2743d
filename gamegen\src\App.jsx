import { useState, useEffect } from 'react';
import { Gamepad2, Sparkles, Settings, Download } from 'lucide-react';
import './App.css';

// Import wizard components (fully restored)
import TemplateSelection from './components/wizard/TemplateSelection';
import AICustomization from './components/wizard/AICustomization';
import ParameterTuning from './components/wizard/ParameterTuning';
import GameExport from './components/wizard/GameExport';

// Import services (will re-enable as needed)
// import assetGenerator from './services/assetGenerator';
// import parameterTuner from './services/parameterTuner';
// import audioManager from './services/audioManager';

function App() {
  const [currentStep, setCurrentStep] = useState(1);
  const [gameData, setGameData] = useState({
    template: null,
    assets: null,
    parameters: null,
    audio: null,
    customizations: {}
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const steps = [
    { id: 1, title: 'Template Selection', icon: Gamepad2, component: TemplateSelection },
    { id: 2, title: 'AI Customization', icon: Sparkles, component: AICustomization },
    { id: 3, title: 'Parameter Tuning', icon: Settings, component: ParameterTuning },
    { id: 4, title: 'Export Game', icon: Download, component: GameExport }
  ];

  const handleStepComplete = async (stepData) => {
    setLoading(true);
    setError(null);

    try {
      const updatedGameData = { ...gameData, ...stepData };
      setGameData(updatedGameData);
      console.log('Step completed with data:', stepData);

      // Auto-advance to next step if not on the last step
      if (currentStep < steps.length) {
        setCurrentStep(currentStep + 1);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleStepBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const resetWizard = () => {
    setCurrentStep(1);
    setGameData({
      template: null,
      assets: null,
      parameters: null,
      audio: null,
      customizations: {}
    });
    setError(null);
  };

  const CurrentStepComponent = steps[currentStep - 1].component;

  return (
    <div className="app">
      <header className="app-header">
        <div className="header-content">
          <div className="logo">
            <Gamepad2 className="logo-icon" />
            <h1>GameGen</h1>
            <span className="tagline">🚀 Enhanced with AI - No-Code Game Creation</span>
          </div>

          {/* Progress indicator */}
          <div className="progress-indicator">
            {steps.map((step, index) => {
              const StepIcon = step.icon;
              const isActive = currentStep === step.id;
              const isCompleted = currentStep > step.id;
              const isDisabled = !step.component; // Show disabled state for not-yet-enabled steps

              return (
                <div
                  key={step.id}
                  className={`progress-step ${isActive ? 'active' : ''} ${isCompleted ? 'completed' : ''} ${isDisabled ? 'disabled' : ''}`}
                >
                  <div className="step-icon">
                    <StepIcon size={20} />
                  </div>
                  <span className="step-title">{step.title}</span>
                  {index < steps.length - 1 && <div className="step-connector" />}
                </div>
              );
            })}
          </div>
        </div>
      </header>

      <main className="app-main">
        {error && (
          <div className="error-banner">
            <p>Error: {error}</p>
            <button onClick={() => setError(null)}>Dismiss</button>
          </div>
        )}

        <div className="wizard-container">
          <div className="wizard-content">
            {CurrentStepComponent ? (
              <CurrentStepComponent
                gameData={gameData}
                onComplete={handleStepComplete}
                onBack={handleStepBack}
                loading={loading}
                canGoBack={currentStep > 1}
                isLastStep={currentStep === steps.length}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '3rem' }}>
                <h2>🚧 Step Under Development</h2>
                <p>This step is being restored. Please use Step 1 (Template Selection) for now.</p>
                <button
                  onClick={() => setCurrentStep(1)}
                  style={{
                    padding: '1rem 2rem',
                    background: '#667eea',
                    color: 'white',
                    border: 'none',
                    borderRadius: '0.5rem',
                    cursor: 'pointer',
                    marginTop: '1rem'
                  }}
                >
                  Go to Template Selection
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Navigation buttons */}
        <div className="wizard-navigation">
          {currentStep > 1 && (
            <button
              className="nav-button secondary"
              onClick={handleStepBack}
              disabled={loading}
            >
              Back
            </button>
          )}

          <div className="nav-spacer" />

          {currentStep === steps.length && (
            <button
              className="nav-button secondary"
              onClick={resetWizard}
              disabled={loading}
            >
              Start Over
            </button>
          )}
        </div>
      </main>

      {loading && (
        <div className="loading-overlay">
          <div className="loading-spinner">
            <Sparkles className="spinner-icon" />
            <p>Processing...</p>
          </div>
        </div>
      )}
    </div>
  );
}

export default App;
